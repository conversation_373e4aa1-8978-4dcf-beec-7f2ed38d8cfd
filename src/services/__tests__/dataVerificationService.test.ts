import mongoose from "mongoose";
import { investmentUniverseConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import DataVerificationService from "../dataVerificationService";
import logger from "../../external-services/loggerService";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAccount,
  buildAddress,
  buildAssetTransaction,
  buildBankAccount,
  buildCashbackTransaction,
  buildChargeTransaction,
  buildContentEntry,
  buildDailySummarySnapshot,
  buildDepositCashTransaction,
  buildHoldingDTO,
  buildMandate,
  buildOrder,
  buildPortfolio,
  buildRebalanceAutomation,
  buildRebalanceTransaction,
  buildReward,
  buildSavingsDividend,
  buildSavingsTopup,
  buildSavingsTopUpAutomation,
  buildSavingsWithdrawal,
  buildSubscription,
  buildTopUpAutomation,
  buildUser,
  buildUserDataRequest,
  buildWealthyhoodDividendTransaction,
  buildWithdrawalCashTransaction
} from "../../tests/utils/generateModels";
import {
  buildWealthkernelCashBalanceResponse,
  buildWealthkernelTransactionResponse,
  buildWealthkernelValuationResponse
} from "../../tests/utils/generateWealthkernel";
import { HoldingsType, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import { KycStatusEnum, UserDocument, UserTypeEnum } from "../../models/User";
import { faker } from "@faker-js/faker";
import {
  AssetTransactionDocument,
  CashbackTransactionDocument,
  ChargeTransactionDocument,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  RebalanceTransactionDocument,
  SavingsDividendTransactionDocument,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument,
  WealthyhoodDividendTransactionDocument,
  WithdrawalCashTransactionDocument
} from "../../models/Transaction";
import DateUtil from "../../utils/dateUtil";
import MailchimpService from "../../external-services/mailchimpService";
import { AccountDocument } from "../../models/Account";
import Decimal from "decimal.js";
import { ProviderEnum } from "../../configs/providersConfig";
import { Subscription } from "../../models/Subscription";
import { UserDataRequestDocument } from "../../models/UserDataRequest";
import { BankAccountDocument, DeactivationReasonEnum } from "../../models/BankAccount";
import { RewardDocument } from "../../models/Reward";
import { CurrencyEnum, WealthkernelService } from "../../external-services/wealthkernelService";
import { AutomationDocument } from "../../models/Automation";
import { ContentEntryDocument } from "../../models/ContentEntry";
import { FinimizeContentTypeEnum } from "../../external-services/finimizeService";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

describe("DataVerificationService", () => {
  beforeEach(() => jest.resetAllMocks());
  beforeAll(async () => await connectDb("DataVerificationService"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("findCashHoldingsMismatches", () => {
    let user: UserDocument;
    const AVAILABLE_CASH = 100;
    const WK_PORTFOLIO_ID = "WK_PORTFOLIO_ID";

    describe("when the portfolio has holdings with no asset transactions created or updated after valuation", () => {
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        {
          assetId: "equities_china",
          quantity: 2,
          price: 20
        },
        { assetId: "equities_eu", quantity: 4, price: 15 },
        {
          assetId: "equities_global",
          quantity: 5,
          price: 10
        },
        { assetId: "equities_uk", quantity: 0.5, price: 15 }
      ];
      const WK_CONFIG_MISMATCHED = ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity }) => ({
        isin: ASSET_CONFIG[assetId].isin,
        quantity: quantity + 1 // forcing quantity to be different
      }));
      const WK_CONFIG_MATCHED = ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity }) => ({
        isin: ASSET_CONFIG[assetId].isin,
        quantity
      }));

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });
        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity }) => buildHoldingDTO(true, assetId, quantity))
        );
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings
        });
      });

      it("should log an error if the cash value in the db is different to wk", async () => {
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: WK_CONFIG_MATCHED.map(({ isin, quantity }) => ({
            isin,
            quantity,
            price: { currency: "GBP", amount: 10 },
            value: { currency: "GBP", amount: 10 * quantity },
            fxRate: 1.0
          }))
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockResolvedValue([wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: AVAILABLE_CASH - 1 }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);

        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenCalledWith(
          expect.stringContaining(`Cash value mismatch for portfolio ${WK_PORTFOLIO_ID}`),
          {
            module: "DataVerificationService",
            method: "_compareDbToWkCash",
            data: { wealthkernelPortfolioId: WK_PORTFOLIO_ID, dbCash: 100, aggregatedDbCash: 100, wkCash: 99 }
          }
        );
      });

      it("should log an error if the portfolio holdings in the db is different to wk", async () => {
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: WK_CONFIG_MISMATCHED.map(({ isin, quantity }) => ({
            isin,
            quantity,
            price: { currency: "GBP", amount: 10 },
            value: { currency: "GBP", amount: 10 * quantity },
            fxRate: 1.0
          }))
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockResolvedValue([wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: AVAILABLE_CASH }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenNthCalledWith(1, `Holdings mismatch for portfolio ${WK_PORTFOLIO_ID}`, {
          module: "DataVerificationService",
          method: "_compareDbToWkInvestmentHoldings",
          data: {
            wealthkernelPortfolioId: WK_PORTFOLIO_ID,
            asset: "equities_china",
            wealthkernelQuantity: 3,
            wealthyhoodQuantity: 2
          }
        });
        expect(logger.error).toHaveBeenNthCalledWith(2, `Holdings mismatch for portfolio ${WK_PORTFOLIO_ID}`, {
          module: "DataVerificationService",
          method: "_compareDbToWkInvestmentHoldings",
          data: {
            wealthkernelPortfolioId: WK_PORTFOLIO_ID,
            asset: "equities_eu",
            wealthkernelQuantity: 5,
            wealthyhoodQuantity: 4
          }
        });
        expect(logger.error).toHaveBeenNthCalledWith(3, `Holdings mismatch for portfolio ${WK_PORTFOLIO_ID}`, {
          module: "DataVerificationService",
          method: "_compareDbToWkInvestmentHoldings",
          data: {
            wealthkernelPortfolioId: WK_PORTFOLIO_ID,
            asset: "equities_global",
            wealthkernelQuantity: 6,
            wealthyhoodQuantity: 5
          }
        });
      });

      it("should log success if cash & holdings match", async () => {
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: WK_CONFIG_MATCHED.map(({ isin, quantity }) => ({
            isin,
            quantity,
            price: { currency: "GBP", amount: 10 },
            value: { currency: "GBP", amount: 10 * quantity },
            fxRate: 1.0
          }))
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockResolvedValue([wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: AVAILABLE_CASH }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    it("should log success when the portfolio document and WK valuation response are empty", async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      const EMPTY_WK_PORTFOLIO_ID = "EMPTY_WK_PORTFOLIO_ID";
      await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: EMPTY_WK_PORTFOLIO_ID, status: "Active" } },
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      const wealthkernelValuation = buildWealthkernelValuationResponse({
        cash: [],
        holdings: []
      });
      jest
        .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
        .mockImplementation(async ({ portfolioId }) =>
          portfolioId === EMPTY_WK_PORTFOLIO_ID ? [wealthkernelValuation] : [buildWealthkernelValuationResponse()]
        );
      jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([]);
      jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);

      await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);
      expect(logger.error).not.toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith(
        "🥳 Cash in database matches wealthkernel data!",
        expect.anything()
      );
      expect(logger.info).toHaveBeenCalledWith(
        "🥳 Holdings in database match wealthkernel data!",
        expect.anything()
      );
    });

    describe("when portfolio has different settled and available cash", () => {
      let user: UserDocument;
      const AVAILABLE_CASH = 100;
      const SETTLED_CASH = 90;
      const WK_PORTFOLIO_ID = "WK_PORTFOLIO_ID";

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: SETTLED_CASH } },
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: AVAILABLE_CASH },
              value: { currency: "GBP", amount: AVAILABLE_CASH },
              fxRate: 1.0
            }
          ],
          holdings: []
        });
        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: AVAILABLE_CASH }
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockResolvedValue([wealthkernelValuation]);
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log an error when settled cash is different from available cash", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenCalledWith(
          `Available cash (${AVAILABLE_CASH}) does not match settled cash (${SETTLED_CASH}) for portfolio ${WK_PORTFOLIO_ID}`,
          {
            module: "DataVerificationService",
            method: "_compareAvailableToSettledCash"
          }
        );
      });
    });

    describe("when portfolio has holdings and asset transactions exist with status 'Pending'", () => {
      // In this scenario, the user:
      // 1. Had £25 in our DB
      // 2. Then created an asset transaction (with a single buy order) of £10
      // 3. Since their asset transaction is pending, we should not try to find mismatches for that user
      const INITIAL_CASH = 25;
      const ORDER_AMOUNT = 10;
      const AVAILABLE_CASH = INITIAL_CASH - ORDER_AMOUNT;
      const HOLDING_QUANTITY = 1;
      const WK_VALUATION_AMOUNT = 10;

      let holdings: HoldingsType[];
      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        holdings = [
          await buildHoldingDTO(true, "equities_uk", HOLDING_QUANTITY, {
            price: 15
          })
        ];

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          wealthkernel: {
            status: "Pending"
          },
          createdAt: new Date()
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: holdings.map((holding) => {
            return {
              isin: ASSET_CONFIG[holding.assetCommonId].isin,
              quantity: holding.quantity,
              price: { currency: "GBP", amount: holding.quantity },
              value: { currency: "GBP", amount: holding.quantity },
              fxRate: 1.0
            };
          }),
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should not try to find mismatches", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);
        expect(logger.info).toHaveBeenCalledWith(
          `User ${user.id.toString()} has pending asset/rebalance transactions, therefore we won't try to find mismatches`,
          expect.anything()
        );
      });
    });

    describe("when portfolio has holdings and a pending reward (reward with order without wealthkernel id)", () => {
      // In this scenario, the user:
      // 1. Has £0 in our DB
      // 2. However, they have a pending reward of £10.
      // 3. That means that their cash in the WK valuation is £10.
      const INITIAL_CASH = 0;
      const WK_VALUATION_AMOUNT = 10;
      const HOLDING_QUANTITY = 1;
      const FEES = {
        FX: 0.01,
        COMMISSION: 0.01,
        EXEC_SPREAD: 0.01
      };
      const REWARD = Decimal.sub(WK_VALUATION_AMOUNT, FEES.FX)
        .sub(FEES.COMMISSION)
        .sub(FEES.EXEC_SPREAD)
        .mul(100)
        .toNumber();

      let holdings: HoldingsType[];
      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        holdings = [
          await buildHoldingDTO(true, "equities_uk", HOLDING_QUANTITY, {
            price: 15
          })
        ];

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: INITIAL_CASH, reserved: 0, settled: INITIAL_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: holdings
        });

        // Eligible reward
        await buildReward({
          referrer: portfolio.owner as mongoose.Types.ObjectId,
          referral: (await buildUser({ kycStatus: KycStatusEnum.PASSED })).id,
          targetUser: portfolio.owner as mongoose.Types.ObjectId,
          consideration: {
            amount: REWARD,
            orderAmount: REWARD,
            bonusAmount: REWARD,
            currency: "GBP"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled",
                submittedAt: DateUtil.getDateForTime(DateUtil.getDateOfDaysAgo(new Date(), 2), {
                  atHours: 11,
                  atMinutes: 0
                })
              }
            }
          },
          fees: {
            fx: {
              amount: FEES.FX,
              currency: "GBP"
            },
            commission: {
              amount: FEES.COMMISSION,
              currency: "GBP"
            },
            executionSpread: {
              amount: FEES.EXEC_SPREAD,
              currency: "GBP"
            }
          }
        });

        // Not eligible reward as deposit is not Settled
        await buildReward({
          referrer: portfolio.owner as mongoose.Types.ObjectId,
          referral: (await buildUser({ kycStatus: KycStatusEnum.PASSED })).id,
          targetUser: portfolio.owner as mongoose.Types.ObjectId,
          consideration: {
            amount: 1000,
            orderAmount: 1000,
            bonusAmount: 1000,
            currency: "GBP"
          },
          deposit: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Created",
              submittedAt: DateUtil.getDateForTime(DateUtil.getDateOfDaysAgo(new Date(), 2), {
                atHours: 11,
                atMinutes: 0
              })
            }
          },
          order: {}
        } as any);

        // Not eligible reward missing deposit Wealthkernel submittedAt after dead line
        await buildReward({
          referrer: portfolio.owner as mongoose.Types.ObjectId,
          referral: (await buildUser({ kycStatus: KycStatusEnum.PASSED })).id,
          targetUser: portfolio.owner as mongoose.Types.ObjectId,
          consideration: {
            amount: 1000,
            orderAmount: 1000,
            bonusAmount: 1000,
            currency: "GBP"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled",
                submittedAt: new Date()
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open",
                submittedAt: DateUtil.getDateForTime(DateUtil.getDateOfDaysAgo(new Date(), 2), {
                  atHours: 11,
                  atMinutes: 0
                })
              }
            }
          }
        });
      });

      it("should log success if portfolio cash match wk aggregated cash", async () => {
        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: holdings.map((holding) => {
            return {
              isin: ASSET_CONFIG[holding.assetCommonId].isin,
              quantity: holding.quantity,
              price: { currency: "GBP", amount: holding.quantity },
              value: { currency: "GBP", amount: holding.quantity },
              fxRate: 1.0
            };
          }),
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);

        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);
        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });

      it("should log an error if the cash value in the db is different to wk", async () => {
        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: holdings.map((holding) => {
            return {
              isin: ASSET_CONFIG[holding.assetCommonId].isin,
              quantity: holding.quantity,
              price: { currency: "GBP", amount: holding.quantity },
              value: { currency: "GBP", amount: holding.quantity },
              fxRate: 1.0
            };
          }),
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT + 1 }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);

        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);
        expect(logger.error).toHaveBeenCalledTimes(1);
      });
    });

    describe("when portfolio has no transactions and have EUR cash", () => {
      // In this scenario, the user:
      // 1. Has €1 in cash
      // 2. In WK, they also have €1 in cash

      const AVAILABLE_CASH = 1;
      const WK_VALUATION_AMOUNT = 1;

      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        await buildPortfolio({
          owner: user.id,
          currency: "EUR",
          cash: { EUR: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "EUR", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when portfolio has no transactions and have GBP cash", () => {
      // In this scenario, the user:
      // 1. Has £1 in cash
      // 2. In WK, they also have £1 in cash

      const AVAILABLE_CASH = 1;
      const WK_VALUATION_AMOUNT = 1;

      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        await buildPortfolio({
          owner: user.id,
          currency: "GBP",
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when portfolio has a pending cash subscription charge", () => {
      // In this scenario, the user:
      // 1. Has £1 in cash
      // 2. At the end of the month, we charged them £1 for their free plan.
      // 3. £1 was reduced from their Wealthyhood cash balance.
      // 4. However, Wealthkernel hasn't created the charge transaction on their side yet.
      // 5. Therefore, their WH cash balance is £0 and their WK is £1

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 1;
      const WK_VALUATION_AMOUNT = 1;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        await buildChargeTransaction({
          chargeMethod: "combined",
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber(),
            cashAmount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when portfolio has a pending direct debit subscription charge", () => {
      // In this scenario, the user:
      // 1. Has £0 in cash
      // 2. At the end of the month, we charged them £2.99 for their paid_low plan (direct debit).

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 2.99;
      const WK_VALUATION_AMOUNT = 0;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "paid_low_monthly",
          category: "FeeBasedSubscription"
        });

        await buildChargeTransaction({
          chargeMethod: "direct-debit",
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when portfolio has a pending card subscription charge", () => {
      // In this scenario, the user:
      // 1. Has £0 in cash
      // 2. At the end of the month, we charged them £2.99 for their paid_low plan (card).

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 2.99;
      const WK_VALUATION_AMOUNT = 0;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "paid_low_monthly",
          category: "FeeBasedSubscription"
        });

        await buildChargeTransaction({
          chargeMethod: "card",
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when portfolio has a settled cash subscription charge", () => {
      // In this scenario, the user:
      // 1. Has £1 in cash
      // 2. At the end of the month, we charged them £1 for their free plan.
      // 3. £1 was reduced from their Wealthyhood cash balance.
      // 4. Wealthkernel has created and settled the charge transaction on their side.
      // 5. Therefore, their WH cash balance is £0 and their WK is also £0

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 1;
      const WK_VALUATION_AMOUNT = 0;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const chargeTransaction = await buildChargeTransaction({
          chargeMethod: "cash",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber(),
            cashAmount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id,
          createdAt: DateUtil.getFirstDayOfLastMonth()
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });

        const wkChargeTransactions = [
          buildWealthkernelTransactionResponse({
            id: chargeTransaction.providers.wealthkernel.id,
            portfolioId: portfolio.providers.wealthkernel.id,
            status: "Settled",
            consideration: {
              currency: CurrencyEnum.GBP,
              amount: CHARGE_AMOUNT
            },
            timestamp: "2022-11-15T12:00:00Z"
          })
        ];

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);
        jest
          .spyOn(WealthkernelService.UKInstance, "listTransactions")
          .mockImplementation(async () => wkChargeTransactions);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when portfolio has a pending combined subscription charge", () => {
      // In this scenario, the user:
      // 1. Has £0.8 in cash and £0.2 in holdings
      // 2. At the end of the month, we charged them £1 for their free plan.
      // 3. £0.8 was reduced from their Wealthyhood cash balance.
      // 4. Wealthkernel hasn't created the charge transaction on their side yet.
      // 5. However, as the order for the holdings part of the user's charge is matched, the £0.2 has been added to the user's WK valuation
      // 5. Therefore, their WH cash balance is £0 and their WK is £1

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 1;
      const CASH_CHARGE_AMOUNT = 0.8;
      const WK_VALUATION_AMOUNT = 1;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const transaction = await buildChargeTransaction({
          chargeMethod: "combined",
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber(),
            cashAmount: Decimal.mul(CASH_CHARGE_AMOUNT, 100).toNumber(),
            holdingsAmount: Decimal.sub(CHARGE_AMOUNT, CASH_CHARGE_AMOUNT).mul(100).toNumber()
          },
          subscription: subscription.id
        });

        await buildOrder({
          side: "Sell",
          transaction: transaction.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: new Date()
            }
          },
          consideration: {
            amount: Decimal.sub(CHARGE_AMOUNT, CASH_CHARGE_AMOUNT).mul(100).toNumber(),
            currency: "GBP"
          },
          quantity: 1,
          isin: ASSET_CONFIG["equities_uk"].isin
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });

        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio has less holdings in WK than in DB", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        // DB portfolio has both EU Stocks and UK Stocks
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_eu", 1), await buildHoldingDTO(true, "equities_uk", 1)]
        });

        // WK portfolio only has EU Stocks
        const wealthkernelHoldings = [
          {
            isin: ASSET_CONFIG["equities_eu"].isin,
            quantity: 1,
            price: { currency: "GBP", amount: 10 },
            value: { currency: "GBP", amount: 10 },
            fxRate: 1.0
          }
        ];
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: wealthkernelHoldings
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockResolvedValue([wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse();
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log an error", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenCalledWith(
          `Holdings length mismatch for portfolio ${WK_PORTFOLIO_ID} - DB holdings include holdings not in WK`,
          {
            module: "DataVerificationService",
            method: "_compareDbToWkInvestmentHoldings",
            data: {
              wealthkernelPortfolioId: WK_PORTFOLIO_ID,
              dbHoldingsNotInWealthkernel: ["equities_uk"]
            }
          }
        );
        expect(logger.info).not.toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio has less holdings in DB than in WK", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        // DB portfolio only has EU Stocks
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: [await buildHoldingDTO(true, "equities_eu", 1)]
        });

        // WK portfolio has both EU Stocks and UK Stocks
        const wealthkernelHoldings = [
          {
            isin: ASSET_CONFIG["equities_eu"].isin,
            quantity: 1,
            price: { currency: "GBP", amount: 10 },
            value: { currency: "GBP", amount: 10 },
            fxRate: 1.0
          },
          {
            isin: ASSET_CONFIG["equities_uk"].isin,
            quantity: 1,
            price: { currency: "GBP", amount: 10 },
            value: { currency: "GBP", amount: 10 },
            fxRate: 1.0
          }
        ];
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: wealthkernelHoldings
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockResolvedValue([wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse();
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);

        jest.spyOn(WealthkernelService.UKInstance, "listTransactions").mockImplementation(async () => []);
      });

      it("should log an error", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenCalledWith(
          `Holdings length mismatch for portfolio ${WK_PORTFOLIO_ID} - WK holdings include holdings not in DB`,
          {
            module: "DataVerificationService",
            method: "_compareDbToWkInvestmentHoldings",
            data: {
              wealthkernelPortfolioId: WK_PORTFOLIO_ID,
              wkHoldingsNotInDatabase: [ASSET_CONFIG["equities_uk"].isin]
            }
          }
        );
        expect(logger.info).not.toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio has a 'holdings' charge with unmatched orders", () => {
      // In this scenario, the user:
      // 1. Has £0 in cash
      // 2. Has a "holdings" charge with unmatched orders
      // 3. Has £0 also in WK cash
      // 5. This case should not cause a mismatch

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 1;
      const WK_VALUATION_AMOUNT = 0;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const chargeTransaction = await buildChargeTransaction({
          chargeMethod: "holdings",
          providers: {
            wealthkernel: {}
          },
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id,
          createdAt: DateUtil.getFirstDayOfLastMonth()
        });
        await buildOrder({
          transaction: chargeTransaction.id,
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Open",
              submittedAt: new Date()
            }
          }
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio has a 'combined' charge with unmatched orders", () => {
      // In this scenario, the user:
      // 0. We want to charge the user £2 from both cash and holdings
      // 1. Has £0 in cash
      // 2. Has a "combined" (cashCharge: £1, holdingsCharge: £1) charge with unmatched orders
      // 3. Has £1 also in WK cash
      // 5. This case should not cause a mismatch

      const AVAILABLE_CASH = 0;
      const HOLDINGS_CHARGE_AMOUNT = 1;
      const CASH_CHARGE_AMOUNT = 1;
      const WK_VALUATION_AMOUNT = 1;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const chargeTransaction = await buildChargeTransaction({
          chargeMethod: "combined",
          providers: {
            wealthkernel: {}
          },
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            currency: "GBP",
            holdingsAmount: Decimal.mul(HOLDINGS_CHARGE_AMOUNT, 100).toNumber(),
            cashAmount: Decimal.mul(CASH_CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id,
          createdAt: DateUtil.getFirstDayOfLastMonth()
        });
        await buildOrder({
          transaction: chargeTransaction.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Open",
              submittedAt: new Date()
            }
          }
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio has a 'holdings' charge with matched orders and the transactions status is 'PendingWealthkernelCharge'", () => {
      // In this scenario, the user:
      // 1. Has £0 in cash
      // 2. Has a "holdings" charge with unmatched orders
      // 3. Has £0 also in WK cash
      // 5. This case should cause a mismatch

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 1;
      const WK_VALUATION_AMOUNT = 0;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const chargeTransaction = await buildChargeTransaction({
          chargeMethod: "holdings",
          providers: {
            wealthkernel: {}
          },
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingWealthkernelCharge",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id,
          createdAt: DateUtil.getFirstDayOfLastMonth()
        });
        await buildOrder({
          transaction: chargeTransaction.id,
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: new Date()
            }
          }
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log the cash mismatch", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenCalledWith(
          `Cash value mismatch for portfolio ${portfolio.providers?.wealthkernel.id} | aggregated DB cash: ${AVAILABLE_CASH + CHARGE_AMOUNT} | WK cash: ${WK_VALUATION_AMOUNT}`,
          expect.anything()
        );
      });
    });

    describe("when the portfolio has a 'holdings' charge with unmatched orders and the transactions status is 'PendingWealthkernelCharge'", () => {
      // In this scenario, the user:
      // 1. Has £0 in cash
      // 2. Has a "holdings" charge with unmatched orders
      // 3. Has £0 also in WK cash
      // 5. This case should not cause a mismatch

      const AVAILABLE_CASH = 0;
      const CHARGE_AMOUNT = 1;
      const WK_VALUATION_AMOUNT = 0;

      let portfolio: PortfolioDocument;
      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const chargeTransaction = await buildChargeTransaction({
          chargeMethod: "holdings",
          providers: {
            wealthkernel: {}
          },
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingWealthkernelCharge",
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          subscription: subscription.id,
          createdAt: DateUtil.getFirstDayOfLastMonth()
        });
        await buildOrder({
          transaction: chargeTransaction.id,
          consideration: {
            currency: "GBP",
            amount: Decimal.mul(CHARGE_AMOUNT, 100).toNumber()
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Open",
              submittedAt: new Date()
            }
          }
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [],
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: WK_VALUATION_AMOUNT }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log success", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Cash in database matches wealthkernel data!",
          expect.anything()
        );
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio has savings holding but WK valuation does not", () => {
      // In this scenario, the user:
      // 1. WK valuation contains no savings holdings
      // 2. Portfolio has 1 savings 'holding'
      // 3. This case should not cause a mismatch

      const AVAILABLE_CASH = 0;
      const SAVINGS_AMOUNT = 1;

      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT, currency: "GBX" }]])
        });

        await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: AVAILABLE_CASH },
              value: { currency: "GBP", amount: AVAILABLE_CASH },
              fxRate: 1.0
            }
          ],
          holdings: [],
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse();
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log an error", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenCalledWith(
          `Holdings length mismatch for portfolio ${wkPortfolioId} - DB holdings include holdings not in WK`,
          {
            module: "DataVerificationService",
            method: "_compareDbToWkSavingsHoldings",
            data: {
              wealthkernelPortfolioId: wkPortfolioId,
              dbHoldingsNotInWealthkernel: ["mmf_dist_gbp"]
            }
          }
        );
        expect(logger.info).not.toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio no savings holding but WK valuation does", () => {
      // In this scenario, the user:
      // 1. WK valuation contains 1 savings holdings
      // 2. Portfolio has no savings 'holding'
      // 3. This case should not cause a mismatch

      const AVAILABLE_CASH = 0;
      const SAVINGS_AMOUNT = 1;

      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          cash: [
            {
              currency: "GBP",
              amount: { currency: "GBP", amount: AVAILABLE_CASH },
              value: { currency: "GBP", amount: AVAILABLE_CASH },
              fxRate: 1.0
            }
          ],
          holdings: [
            {
              isin: SAVINGS_PRODUCT_CONFIG_GLOBAL.mmf_dist_gbp.isin,
              quantity: SAVINGS_AMOUNT,
              price: { currency: "GBP", amount: 1 },
              value: { currency: "GBP", amount: SAVINGS_AMOUNT }
            }
          ],
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse();
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log an error", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenCalledWith(
          `Holdings length mismatch for portfolio ${wkPortfolioId} - WK holdings include holdings not in DB`,
          {
            module: "DataVerificationService",
            method: "_compareDbToWkSavingsHoldings",
            data: {
              wealthkernelPortfolioId: wkPortfolioId,
              wkHoldingsNotInDatabase: ["IE00B3L10356"]
            }
          }
        );
        expect(logger.info).not.toHaveBeenCalledWith(
          "🥳 Holdings in database match wealthkernel data!",
          expect.anything()
        );
      });
    });

    describe("when the portfolio has savings holding but WK valuation does with different quantity", () => {
      // In this scenario, the user:
      // 1. WK valuation contains 1 savings holdings
      // 2. Portfolio has 1 savings 'holding' but with different quantity than WK
      // 3. This case should not cause a mismatch

      const AVAILABLE_CASH = 0;
      const SAVINGS_AMOUNT = 1;
      const WK_SAVINGS_AMOUNT = 5;

      const wkPortfolioId = faker.string.uuid();

      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });

        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: AVAILABLE_CASH } },
          providers: { wealthkernel: { id: wkPortfolioId, status: "Active" } },
          holdings: [],
          savings: new Map([["mmf_dist_gbp", { amount: SAVINGS_AMOUNT * 100, currency: "GBX" }]])
        });

        await buildSubscription({
          owner: user.id,
          price: "free_monthly",
          category: "FeeBasedSubscription"
        });

        const valuationDate = new Date();
        valuationDate.setHours(0);
        valuationDate.setMinutes(0);
        const wealthkernelValuation = buildWealthkernelValuationResponse({
          holdings: [
            {
              isin: SAVINGS_PRODUCT_CONFIG_GLOBAL.mmf_dist_gbp.isin,
              quantity: WK_SAVINGS_AMOUNT,
              price: { currency: "GBP", amount: 1 },
              value: { currency: "GBP", amount: WK_SAVINGS_AMOUNT }
            }
          ],
          changedAt: valuationDate
        });
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveValuations")
          .mockImplementation(async () => [wealthkernelValuation]);

        const wealthkernelBalance = buildWealthkernelCashBalanceResponse({
          value: { currency: "GBP", amount: AVAILABLE_CASH }
        });
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([wealthkernelBalance]);
      });

      it("should log an error", async () => {
        await DataVerificationService.findCashHoldingsMismatches(WealthkernelService.UKInstance);

        expect(logger.error).toHaveBeenNthCalledWith(1, `Holdings mismatch for portfolio ${wkPortfolioId}`, {
          module: "DataVerificationService",
          method: "_compareDbToWkSavingsHoldings",
          data: {
            wealthkernelPortfolioId: wkPortfolioId,
            asset: "mmf_dist_gbp",
            wealthkernelQuantity: WK_SAVINGS_AMOUNT,
            wealthyhoodQuantity: SAVINGS_AMOUNT
          }
        });
      });
    });
  });

  describe("findDuplicateRepeatingTopUps", () => {
    describe("when there are no repeating top-ups for an active automation within the last 30 days", () => {
      beforeEach(async () => {
        const user = await buildUser();
        const automation = await buildTopUpAutomation({ active: true, owner: user });
        const deposits = await DepositCashTransaction.find({ linkedAutomation: automation._id });
        expect(deposits.length).toEqual(0);

        await DataVerificationService.findDuplicateRepeatingTopUps();
      });

      it("should log a success message", () => {
        expect(logger.info).toHaveBeenCalledWith("🥳 No duplicate repeating top-ups found!", expect.anything());
      });
    });

    describe("when there are two repeating top-ups for the same user within the last 20 days but they correspond to different automation", () => {
      beforeEach(async () => {
        const user = await buildUser();

        const timeNow = new Date();
        const automationTopUp = await buildTopUpAutomation({ active: true, owner: user });
        await buildDepositCashTransaction({
          linkedAutomation: automationTopUp._id,
          owner: user._id,
          createdAt: timeNow
        });
        const automationSavings = await buildSavingsTopUpAutomation({ active: true, owner: user._id });
        await buildDepositCashTransaction({
          linkedAutomation: automationSavings._id,
          owner: user._id,
          createdAt: timeNow
        });

        await DataVerificationService.findDuplicateRepeatingTopUps();
      });

      it("should log a success message", () => {
        expect(logger.info).toHaveBeenCalledWith("🥳 No duplicate repeating top-ups found!", expect.anything());
      });
    });

    describe("when there is only one repeating top-up for an active automation within the last 30 days", () => {
      beforeEach(async () => {
        const user = await buildUser();

        const SIXTY_DAYS_AGO = DateUtil.getDateOfDaysAgo(new Date(), 60);
        const automationTopUp = await buildTopUpAutomation({ active: true, owner: user });
        await buildDepositCashTransaction({
          linkedAutomation: automationTopUp._id,
          owner: user._id,
          createdAt: new Date()
        });
        await buildDepositCashTransaction({
          linkedAutomation: automationTopUp._id,
          owner: user._id,
          createdAt: SIXTY_DAYS_AGO
        });

        await DataVerificationService.findDuplicateRepeatingTopUps();
      });

      it("should log a success message", () => {
        expect(logger.info).toHaveBeenCalledWith("🥳 No duplicate repeating top-ups found!", expect.anything());
      });
    });

    describe("when there are two repeating top-ups for an acive automation within the last 30 days", () => {
      it("should log a success message if they have been created more than 20 days ago", async () => {
        const user = await buildUser();

        const TWENTY_FIVE_DAYS_AGO = DateUtil.getDateOfDaysAgo(new Date(), 25);
        const automationTopUp = await buildTopUpAutomation({ active: true, owner: user });
        await buildDepositCashTransaction({
          linkedAutomation: automationTopUp._id,
          owner: user._id,
          createdAt: new Date()
        });
        await buildDepositCashTransaction({
          linkedAutomation: automationTopUp._id,
          owner: user._id,
          createdAt: TWENTY_FIVE_DAYS_AGO
        });

        await DataVerificationService.findDuplicateRepeatingTopUps();

        expect(logger.info).toHaveBeenCalledWith("🥳 No duplicate repeating top-ups found!", expect.anything());
      });

      it("should log an error message if they have been created within 20 days", async () => {
        const user = await buildUser();

        const TWENTY_FIVE_DAYS_AGO = DateUtil.getDateOfDaysAgo(new Date(), 15);
        const automationTopUp = await buildTopUpAutomation({ active: true, owner: user });
        await buildDepositCashTransaction({
          linkedAutomation: automationTopUp._id,
          owner: user._id,
          createdAt: new Date()
        });
        await buildDepositCashTransaction({
          linkedAutomation: automationTopUp._id,
          owner: user._id,
          createdAt: TWENTY_FIVE_DAYS_AGO
        });

        await DataVerificationService.findDuplicateRepeatingTopUps();

        expect(logger.error).toHaveBeenCalledWith(
          expect.stringContaining(`Potentially duplicate TopUpAutomation top-up for user ${user.email}`),
          expect.anything()
        );
      });
    });
  });

  describe("checkMailchimpState", () => {
    let user: UserDocument;

    describe("when the user was created more than 13 months ago", () => {
      beforeEach(async () => {
        const ONE_YEAR_AND_ONE_MONTH_IN_DAYS = 395;
        user = await buildUser({
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), ONE_YEAR_AND_ONE_MONTH_IN_DAYS)
        });
        // The below should normally log an error because of mismatch between REFERRED/REFERREDBY.
        // However, as the user was created more than 13 months ago, we should ignore that.
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "True",
            REFERREDBY: "",
            STATUS: "Investment Created"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has role ADMIN", () => {
      beforeEach(async () => {
        user = await buildUser({ role: [UserTypeEnum.ADMIN] });
        // The below should normally log an error because of mismatch between REFERRED/REFERREDBY.
        // However, as the user is ADMIN, we should ignore that.
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "True",
            REFERREDBY: "",
            STATUS: "Investment Created"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has closed his account and is deleted", () => {
      beforeEach(async () => {
        user = await buildUser({ email: "<EMAIL>" });
        await buildUserDataRequest({ owner: user.id, requestType: "disassociation" });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user Mailchimp status unsubscribed", () => {
      beforeEach(async () => {
        user = await buildUser();
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "unsubscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "<EMAIL>",
            STATUS: "Personal Details Submitted"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp status pending", () => {
      beforeEach(async () => {
        user = await buildUser();
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "pending",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "<EMAIL>",
            STATUS: "Personal Details Submitted"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp status transactional", () => {
      beforeEach(async () => {
        user = await buildUser();
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "transactional",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Investment Succeeded",
            AUTREBALAN: "False"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp status archived", () => {
      beforeEach(async () => {
        user = await buildUser();
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "archived",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Investment Succeeded",
            AUTREBALAN: "False"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp status cleaned", () => {
      beforeEach(async () => {
        user = await buildUser();
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "cleaned",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Investment Succeeded",
            AUTREBALAN: "False"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp REFERREDBY set to a white-listed e-mail", () => {
      beforeEach(async () => {
        user = await buildUser();
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "<EMAIL>",
            STATUS: "Tax Details Submitted"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp AUTREBALAN set to False but they have an active rebalance automation", () => {
      beforeEach(async () => {
        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildRebalanceAutomation({ owner: user.id, portfolio: portfolio.id, active: true });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Investment Succeeded",
            AUTREBALAN: "False"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Rebalance automation state in Mailchimp does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              rebalanceAutomation: "False"
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp AUTREBALAN empty but they have an active rebalance automation", () => {
      beforeEach(async () => {
        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildRebalanceAutomation({ owner: user.id, portfolio: portfolio.id, active: true });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Investment Succeeded"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Rebalance automation state in Mailchimp does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              rebalanceAutomation: undefined
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp AUTREBALAN empty but they have an active rebalance automation AND they should have status Verified but they do not", () => {
      const mailchimpStatus = "Tax Details Submitted";

      beforeEach(async () => {
        user = await buildUser({ kycStatus: "passed" });
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildRebalanceAutomation({ owner: user.id, portfolio: portfolio.id, active: true });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log two validation errors", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenNthCalledWith(
          1,
          `Rebalance automation state in Mailchimp does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              rebalanceAutomation: undefined
            })
          }
        );

        expect(logger.error).toHaveBeenNthCalledWith(
          2,
          `Status in Mailchimp should be Verified for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REPINVESTM set to False but they have an active top-up automation", () => {
      beforeEach(async () => {
        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0] });
        await buildTopUpAutomation({ owner: user.id, portfolio: portfolio.id, active: true, mandate: mandate.id });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Investment Succeeded",
            REPINVESTM: "False"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Top-up automation state in Mailchimp does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              topUpAutomation: "False"
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REPINVESTM empty but they have an active top-up automation", () => {
      beforeEach(async () => {
        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0] });
        await buildTopUpAutomation({ owner: user.id, portfolio: portfolio.id, active: true, mandate: mandate.id });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Investment Succeeded"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Top-up automation state in Mailchimp does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              topUpAutomation: undefined
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REPINVESTM empty but they have an active top-up automation AND they should have status Verified but they do not", () => {
      const mailchimpStatus = "Tax Details Submitted";

      beforeEach(async () => {
        user = await buildUser({ kycStatus: "passed" });
        const portfolio = await buildPortfolio({ owner: user.id });
        const mandate = await buildMandate({ owner: user.id, bankAccount: user.bankAccounts[0] });
        await buildTopUpAutomation({ owner: user.id, portfolio: portfolio.id, active: true, mandate: mandate.id });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log two validation errors", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenNthCalledWith(
          1,
          `Top-up automation state in Mailchimp does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              topUpAutomation: undefined
            })
          }
        );

        expect(logger.error).toHaveBeenNthCalledWith(
          2,
          `Status in Mailchimp should be Verified for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REFERRED set to false and REFERREDBY is empty", () => {
      beforeEach(async () => {
        user = await buildUser();
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "False",
            REFERREDBY: "",
            STATUS: "Tax Details Submitted"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp REFERREDBY set to an email but REFERRED is empty", () => {
      beforeEach(async () => {
        user = await buildUser({ referredByEmail: faker.internet.email() });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERREDBY: user.referredByEmail,
            REFERRED: "",
            STATUS: "Investment Created"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Referral state in Mailchimp is invalid or does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              referred: "",
              referredBy: user.referredByEmail
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REFERREDBY empty but we have one in our DB", () => {
      beforeEach(async () => {
        user = await buildUser({ referredByEmail: faker.internet.email() });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: "Investment Created"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Referral state in Mailchimp is invalid or does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              referred: "",
              referredBy: "",
              referredByEmail: user.referredByEmail
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REFERREDBY different to the one in our DB", () => {
      const referredByEmailMailchimp = faker.internet.email();

      beforeEach(async () => {
        user = await buildUser({ referredByEmail: faker.internet.email() });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "True",
            REFERREDBY: referredByEmailMailchimp,
            STATUS: "Investment Created"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Referral state in Mailchimp is invalid or does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              referred: "True",
              referredBy: referredByEmailMailchimp,
              referredByEmail: user.referredByEmail
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REFERREDBY different to the one in our DB AND they should have status Verified but they do not", () => {
      const referredByEmailMailchimp = faker.internet.email();
      const mailchimpStatus = "Tax Details Submitted";

      beforeEach(async () => {
        user = await buildUser(
          { referredByEmail: faker.internet.email(), kycStatus: KycStatusEnum.PASSED },
          false
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "True",
            REFERREDBY: referredByEmailMailchimp,
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log two validation errors", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenNthCalledWith(
          1,
          `Referral state in Mailchimp is invalid or does not match DB state for user ${user.email}`,
          {
            module: "DataVerificationService",
            method: "checkMailchimpState",
            data: expect.objectContaining({
              email: user.email,
              referred: "True",
              referredBy: referredByEmailMailchimp,
              referredByEmail: user.referredByEmail
            })
          }
        );

        expect(logger.error).toHaveBeenNthCalledWith(
          2,
          `Status in Mailchimp should be Verified for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user has Mailchimp REFERREDBY and REFERRED=true and they match our DB", () => {
      const referredByEmail = faker.internet.email();

      beforeEach(async () => {
        user = await buildUser({ referredByEmail });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "True",
            REFERREDBY: referredByEmail,
            STATUS: "Tax Details Submitted"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp REFERREDBY and REFERRED=affiliate and they match our DB", () => {
      const referredByEmail = faker.internet.email();

      beforeEach(async () => {
        user = await buildUser({ referredByEmail });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "Affiliate",
            REFERREDBY: referredByEmail,
            STATUS: "Tax Details Submitted"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp status Investment Created with a top up transaction and they match our DB", () => {
      const mailchimpStatus = "Investment Created";

      beforeEach(async () => {
        user = await buildUser();
        const automation = await buildTopUpAutomation({ owner: user.id });

        const deposit = await buildDepositCashTransaction(
          {
            owner: user.id,
            directDebit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
            },
            linkedAutomation: automation._id,
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
          },
          user
        );

        await buildAssetTransaction({
          owner: user.id,
          status: "PendingDeposit",
          linkedAutomation: automation._id,
          pendingDeposit: deposit._id
        });

        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus,
            REPINVESTM: "True"
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp status Deposit Succeeded with a cancelled asset transaction and they match our DB", () => {
      const mailchimpStatus = "Deposit Succeeded";

      beforeEach(async () => {
        user = await buildUser();
        const deposit = await buildDepositCashTransaction(
          {
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } },
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            settledAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
          },
          user
        );
        await buildAssetTransaction({
          owner: user.id,
          status: "Cancelled",
          pendingDeposit: deposit.id,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
        });

        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has Mailchimp status Deposit Succeeded with a cancelled asset transaction and a settled deposit (that are not linked) and they match our DB", () => {
      const mailchimpStatus = "Deposit Succeeded";

      beforeEach(async () => {
        user = await buildUser();
        await buildDepositCashTransaction(
          {
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } },
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 3),
            settledAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
          },
          user
        );
        await buildAssetTransaction({
          owner: user.id,
          status: "Cancelled",
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
        });

        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user should have Mailchimp status Tax Details Submitted but they have Verification Failed", () => {
      const mailchimpStatus = "Verification Failed";

      beforeEach(async () => {
        user = await buildUser(
          {
            taxResidency: {
              countryCode: "GB",
              proofType: "NINO",
              value: "*********"
            },
            kycStatus: KycStatusEnum.PENDING
          },
          false
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user should have Mailchimp status Investment Created (because of repeating investment with pending deposit) but they do not", () => {
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        const automation = await buildTopUpAutomation({ owner: user.id });
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          status: "Pending",
          linkedAutomation: automation.id
        });
        await buildAssetTransaction({
          owner: user.id,
          status: "PendingDeposit",
          linkedAutomation: automation.id,
          pendingDeposit: deposit.id
        });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus,
            REPINVESTM: "True"
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Investment Created but they do not", () => {
      const mailchimpStatus = "Deposit Succeeded";

      beforeEach(async () => {
        user = await buildUser();
        await buildAssetTransaction({ owner: user.id, status: "Pending" });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user should have Mailchimp status Investment Succeeded but they do not", () => {
      const mailchimpStatus = "Investment Created";

      beforeEach(async () => {
        user = await buildUser();
        // User has a settled deposit from yesterday and a settled asset transaction today
        await buildAssetTransaction({
          user,
          status: "Settled",
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          settledAt: new Date()
        });
        await buildDepositCashTransaction(
          {
            status: "Settled",
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            settledAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
          },
          user
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user should have Mailchimp status Deposit Created but they do not", () => {
      const mailchimpStatus = "Investment Succeeded";

      beforeEach(async () => {
        user = await buildUser();
        // User has deposited & invested 5 days ago. However, since they deposited again
        // today, their Mailchimp status should be Deposit Created
        await buildAssetTransaction({
          owner: user.id,
          status: "Settled",
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 5)
        });
        await buildDepositCashTransaction(
          {
            owner: user.id,
            status: "Settled",
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 5)
          },
          user
        );
        await buildDepositCashTransaction(
          { owner: user.id, providers: { wealthkernel: { id: faker.string.uuid(), status: "Created" } } },
          user
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user should have Mailchimp status Deposit Succeeded but they do not", () => {
      const mailchimpStatus = "Deposit Created";

      beforeEach(async () => {
        user = await buildUser();
        await buildDepositCashTransaction(
          {
            owner: user.id,
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Settled" },
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              }
            },
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
            settledAt: new Date()
          },
          user
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user should have Mailchimp an Investment Succeeded (because of a settled reward) but they do not", () => {
      // When the user has a reward for which we have a created an order, they should have the 'Investment Created' status
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched",
                submittedAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
              }
            }
          },
          updatedAt: new Date()
        });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Investment Created (because of a pending reward) but they do not", () => {
      // When the user has a reward for which we have a created an order, they should have the 'Investment Created' status
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          quantity: 1,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Open",
                submittedAt: new Date()
              }
            }
          }
        });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp an Investment Succeeded (because of a settled savings topup) but they do not", () => {
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        await buildSavingsTopup({
          owner: user.id,
          status: "Settled"
        });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp an Investment Succeeded (because of a settled savings withdrawal) but they do not", () => {
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        await buildSavingsWithdrawal({
          owner: user.id,
          status: "Settled"
        });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Investment Created (because of a pending savings topup) but they do not", () => {
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        await buildSavingsTopup({
          owner: user.id,
          status: "Pending"
        });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Investment Created (because of a pending savings withdrawal) but they do not", () => {
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        await buildSavingsWithdrawal({
          owner: user.id,
          status: "Pending"
        });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Deposit Succeeded (has both a settled and a cancelled payment) but they do not", () => {
      const mailchimpStatus = "Verified";

      beforeEach(async () => {
        user = await buildUser();
        // The user has two deposits, one older (settled) and one new (cancelled).
        // The expected status for that user is Deposit Succeeded.
        await buildDepositCashTransaction(
          {
            owner: user.id,
            providers: { truelayer: { id: faker.string.uuid(), status: "failed" } },
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
          },
          user
        );
        await buildDepositCashTransaction(
          {
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } },
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 5),
            settledAt: DateUtil.getDateOfDaysAgo(new Date(), 3)
          },
          user
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Deposit Created/Deposit Succeeded/Investment Created/Investment Succeeded for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Verified but they do not", () => {
      const mailchimpStatus = "Tax Details Submitted";

      beforeEach(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED }, false);
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Verified for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Tax Details Submitted but they do not", () => {
      const mailchimpStatus = "Address Submitted";

      beforeEach(async () => {
        user = await buildUser(
          {
            employmentInfo: undefined,
            taxResidency: {
              countryCode: "GB",
              proofType: "NINO",
              value: "*********"
            },
            kycStatus: KycStatusEnum.PENDING
          },
          false
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Tax Details Submitted/Personal Details Submitted for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Address Submitted but they do not", () => {
      const mailchimpStatus = "Passport Details Submitted";

      beforeEach(async () => {
        user = await buildUser(
          {
            employmentInfo: undefined,
            taxResidency: undefined,
            kycStatus: KycStatusEnum.PENDING
          },
          false
        );
        await buildAddress({ owner: user.id });
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Address Submitted for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Passport Details Submitted but they do not", () => {
      const mailchimpStatus = "Signed Up";

      beforeEach(async () => {
        user = await buildUser(
          {
            employmentInfo: undefined,
            taxResidency: undefined,
            kycStatus: KycStatusEnum.PENDING
          },
          false
        );
        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: mailchimpStatus
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Passport Details Submitted for ${user.email} but is ${mailchimpStatus}`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email,
              status: mailchimpStatus
            })
          }
        );
      });
    });

    describe("when the user should have Mailchimp status Signed Up but they do not", () => {
      beforeEach(async () => {
        user = await buildUser(
          {
            employmentInfo: undefined,
            taxResidency: undefined,
            kycStatus: KycStatusEnum.PENDING,
            nationalities: []
          },
          false
        );

        jest.spyOn(MailchimpService, "getMergeFieldsAndStatusForUserEmail").mockResolvedValue({
          status: "subscribed",
          mergeFields: {
            REFERRED: "",
            REFERREDBY: "",
            STATUS: undefined
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkMailchimpState();

        expect(logger.error).toHaveBeenCalledWith(
          `Status in Mailchimp should be Signed Up for ${user.email} but is undefined`,
          {
            module: "DataVerificationService",
            method: "_compareMailchimpToDbStatus",
            data: expect.objectContaining({
              email: user.email
            })
          }
        );
      });
    });
  });

  describe("checkNonConvertedUsers", () => {
    let user: UserDocument;

    describe("when we have the same amount of converted users and unique owners of settled asset transactions", () => {
      beforeEach(async () => {
        // We set up two converted users with settled asset transactions and one non-converted user
        const [firstUser, secondUser] = await Promise.all([
          buildUser({ portfolioConversionStatus: "completed" }),
          buildUser({ portfolioConversionStatus: "completed" }),
          buildUser({ portfolioConversionStatus: "notStarted" })
        ]);
        await Promise.all([
          buildAssetTransaction({ owner: firstUser.id, status: "Settled" }),
          buildAssetTransaction({ owner: secondUser.id, status: "Settled" }),
          buildAssetTransaction({ owner: secondUser.id, status: "Settled" }),
          buildAssetTransaction({ owner: secondUser.id, status: "Pending" })
        ]);
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.info).toHaveBeenCalled();
      });
    });

    describe("when the user has no settled investments and their conversion is notStarted", () => {
      beforeEach(async () => {
        await buildUser({ portfolioConversionStatus: "notStarted" });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has a pending investment and their conversion is inProgress", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "inProgress" });
        await buildAssetTransaction({ owner: user.id, status: "Pending" });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has a settled investment and their conversion is completed", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });
        await buildAssetTransaction({ owner: user.id, status: "Settled" });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user has no settled investments and their conversion is completed", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "completed" });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.error).toHaveBeenCalledWith("❌ DB state for converted users is invalid!", {
          module: "DataVerificationService",
          method: "checkNonConvertedUsers",
          data: expect.objectContaining({
            convertedUsersWithoutSettledInvestments: [user.id],
            nonConvertedOwnersOfSettledInvestments: []
          })
        });
      });
    });

    describe("when the user has a settled investment and their conversion is notStarted", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "notStarted" });
        await buildAssetTransaction({ owner: user.id, status: "Settled" });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.error).toHaveBeenCalledWith("❌ DB state for converted users is invalid!", {
          module: "DataVerificationService",
          method: "checkNonConvertedUsers",
          data: expect.objectContaining({
            convertedUsersWithoutSettledInvestments: [],
            nonConvertedOwnersOfSettledInvestments: [user.id]
          })
        });
      });
    });

    describe("when the user has a settled investment and their conversion is inProgress", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "inProgress" });
        await buildAssetTransaction({ owner: user.id, status: "Settled" });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.error).toHaveBeenCalledWith("❌ DB state for converted users is invalid!", {
          module: "DataVerificationService",
          method: "checkNonConvertedUsers",
          data: expect.objectContaining({
            convertedUsersWithoutSettledInvestments: [],
            nonConvertedOwnersOfSettledInvestments: [user.id]
          })
        });
      });
    });

    describe("when the user has a settled reward and their conversion is notStarted", () => {
      beforeEach(async () => {
        user = await buildUser({ portfolioConversionStatus: "notStarted" });
        await buildReward({
          targetUser: user.id,
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkNonConvertedUsers();

        expect(logger.error).toHaveBeenCalledWith("❌ DB state for converted users is invalid!", {
          module: "DataVerificationService",
          method: "checkNonConvertedUsers",
          data: expect.objectContaining({
            convertedUsersWithoutSettledInvestments: [],
            nonConvertedOwnersOfSettledInvestments: [user.id]
          })
        });
      });
    });
  });

  describe("checkModelState", () => {
    let user: UserDocument;

    describe("when the user is not KYC passed", () => {
      beforeEach(async () => {
        await buildUser({ kycStatus: KycStatusEnum.PENDING });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when the user does not have an address", () => {
      beforeEach(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED }, false);
        const account: AccountDocument = await buildAccount({ owner: user.id });
        await Promise.all([buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, account: account.id })]);
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).toHaveBeenCalledWith(`User ${user.email} has 0 addresses`, {
          method: "checkModelState",
          module: "DataVerificationService",
          userEmail: user.email
        });
      });
    });

    describe("when the user has more than one address", () => {
      beforeEach(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED }, false);
        const account: AccountDocument = await buildAccount({ owner: user.id });
        await Promise.all([
          buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, account: account.id }),
          buildAddress({ owner: user.id }),
          buildAddress({ owner: user.id })
        ]);
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).toHaveBeenCalledWith(`User ${user.email} has 2 addresses`, {
          method: "checkModelState",
          module: "DataVerificationService",
          userEmail: user.email
        });
      });
    });

    describe("when the user does not have an account", () => {
      beforeEach(async () => {
        const anotherUser = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        const accountBelongingToOtherUser = await buildAccount({ owner: anotherUser.id });

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED }, false);
        await Promise.all([
          buildPortfolio(
            { owner: user.id, mode: PortfolioModeEnum.REAL, account: accountBelongingToOtherUser.id },
            false
          ),
          buildAddress({ owner: user.id })
        ]);
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).toHaveBeenCalledWith(`User ${user.email} has 0 accounts`, {
          method: "checkModelState",
          module: "DataVerificationService",
          userEmail: user.email
        });
      });
    });

    describe("when the user has more than one account", () => {
      beforeEach(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED }, false);
        const account: AccountDocument = await buildAccount({ owner: user.id });
        await Promise.all([
          buildAccount({ owner: user.id }),
          buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, account: account.id }),
          buildAddress({ owner: user.id })
        ]);
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).toHaveBeenCalledWith(`User ${user.email} has 2 accounts`, {
          method: "checkModelState",
          module: "DataVerificationService",
          userEmail: user.email
        });
      });
    });

    describe("when the user has only one portfolio but it's virtual", () => {
      beforeEach(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, createdAt: new Date(Date.now()) }, false);
        await buildAccount({ owner: user.id });
        await Promise.all([buildPortfolio({ owner: user.id, mode: "VIRTUAL" }), buildAddress({ owner: user.id })]);
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).toHaveBeenCalledWith(`User ${user.email} has 0 portfolios`, {
          method: "checkModelState",
          module: "DataVerificationService",
          userEmail: user.email
        });
      });
    });

    describe("when the user has two portfolios", () => {
      beforeEach(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, createdAt: new Date(Date.now()) }, false);
        const account: AccountDocument = await buildAccount({ owner: user.id });
        await Promise.all([
          buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, account: account.id }),
          buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, account: account.id }),
          buildAddress({ owner: user.id })
        ]);
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).toHaveBeenCalledWith(`User ${user.email} has 2 portfolios`, {
          method: "checkModelState",
          module: "DataVerificationService",
          userEmail: user.email
        });
      });
    });

    describe("when the user has 1 portfolio, one address and one account", () => {
      beforeEach(async () => {
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, createdAt: new Date(Date.now()) }, false);
        const account: AccountDocument = await buildAccount({ owner: user.id });
        await Promise.all([
          buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, account: account.id }),
          buildAddress({ owner: user.id })
        ]);
      });

      it("should log an info success message", async () => {
        await DataVerificationService.checkModelState();

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith(
          "🥳 Completed user model state data verification check, no errors found!",
          {
            module: "DataVerificationService",
            method: "checkModelState"
          }
        );
      });
    });
  });

  describe("checkStagnantTransactions", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL
      });
    });

    describe("when there is a withdrawal transaction which has been pending for two work days", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildWithdrawalCashTransaction({
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a withdrawal transaction which has been pending for four days", () => {
      let withdrawal: WithdrawalCashTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 4)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: withdrawal.id,
                createdAt: withdrawal.createdAt,
                owner: withdrawal.owner.toString(),
                email: user.email,
                category: withdrawal.category,
                consideration: withdrawal.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a charge transaction which has been created last month but is not submitted", () => {
      let charge: ChargeTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        charge = await buildChargeTransaction({
          owner: user.id,
          status: "Pending",
          chargeType: "commission",
          chargeMethod: "orders",
          portfolio: portfolio.id,
          createdAt: DateUtil.getFirstDayOfLastMonth(FRIDAY)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: charge.id,
                createdAt: charge.createdAt,
                owner: charge.owner.toString(),
                email: user.email,
                category: charge.category,
                consideration: charge.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a charge transaction which has been created this month but is not submitted", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildChargeTransaction({
          owner: user.id,
          status: "Pending",
          chargeType: "commission",
          chargeMethod: "orders",
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 1) // Created 2022-08-03
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a charge transaction which has been submitted 2 days ago and is still pending", () => {
      let charge: ChargeTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        charge = await buildChargeTransaction({
          owner: user.id,
          status: "Pending",
          chargeType: "commission",
          chargeMethod: "orders",
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 36),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Requested",
              submittedAt: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
            }
          }
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: charge.id,
                createdAt: charge.createdAt,
                owner: charge.owner.toString(),
                email: user.email,
                category: charge.category,
                consideration: charge.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a charge transaction which has been submitted 2 hours ago and is still pending", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildChargeTransaction({
          owner: user.id,
          status: "Pending",
          chargeType: "commission",
          chargeMethod: "orders",
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 36),
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Requested",
              submittedAt: DateUtil.getDateOfHoursAgo(FRIDAY, 2)
            }
          }
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a charge transaction with card payment which has been created 10 work days ago and it is still pending", () => {
      let cardCharge: ChargeTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        cardCharge = await buildChargeTransaction({
          owner: user.id,
          status: "Pending",
          chargeType: "subscription",
          chargeMethod: "card",
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 14)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: cardCharge.id,
                createdAt: cardCharge.createdAt,
                owner: cardCharge.owner.toString(),
                email: user.email,
                category: cardCharge.category,
                consideration: cardCharge.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a charge transaction with card payment which has been created 2 days ago", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildChargeTransaction({
          owner: user.id,
          status: "Pending",
          chargeType: "subscription",
          chargeMethod: "card",
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    // this is a temporary test until we create checks stagnant transaction for orders with lifetime / direct-debit as charge method
    describe("when there is a charge transaction with lifetime or direct-debit which has been created last month but is not submitted", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await Promise.all([
          buildChargeTransaction({
            owner: user.id,
            status: "Pending",
            chargeType: "subscription",
            chargeMethod: "direct-debit",
            portfolio: portfolio.id,
            createdAt: DateUtil.getFirstDayOfLastMonth(FRIDAY)
          }),
          buildChargeTransaction({
            owner: user.id,
            status: "Pending",
            chargeType: "subscription",
            chargeMethod: "lifetime-payment",
            portfolio: portfolio.id,
            createdAt: DateUtil.getFirstDayOfLastMonth(FRIDAY)
          })
        ]);
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a settled asset transaction which was created three days ago", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildAssetTransaction({
          portfolio: portfolio.id,
          status: "Settled",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a settled deposit transaction which was created three days ago", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildDepositCashTransaction({
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3),
          status: "Settled"
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an asset transaction which has been pending for one work day", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildAssetTransaction({
          portfolio: portfolio.id,
          wealthkernel: {
            status: "Pending"
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 1)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there are asset transactions with Pending/PendingGift/PendingDeposit status for three work days", () => {
      let pendingTransaction: AssetTransactionDocument;
      let pendingGiftTransaction: AssetTransactionDocument;
      let pendingDepositTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        pendingTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          wealthkernel: {
            status: "Pending"
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
        });

        pendingGiftTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          wealthkernel: {
            status: "PendingGift"
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
        });

        pendingDepositTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          wealthkernel: {
            status: "PendingDeposit"
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
        });
      });

      it("should log a validation error for all stagnant transactions", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 3 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: pendingTransaction.id,
                createdAt: pendingTransaction.createdAt,
                owner: pendingTransaction.owner.toString(),
                category: pendingTransaction.category,
                email: user.email,
                consideration: pendingTransaction.consideration
              },
              {
                id: pendingGiftTransaction.id,
                createdAt: pendingGiftTransaction.createdAt,
                owner: pendingGiftTransaction.owner.toString(),
                category: pendingGiftTransaction.category,
                email: user.email,
                consideration: pendingGiftTransaction.consideration
              },
              {
                id: pendingDepositTransaction.id,
                createdAt: pendingDepositTransaction.createdAt,
                owner: pendingDepositTransaction.owner.toString(),
                category: pendingDepositTransaction.category,
                email: user.email,
                consideration: pendingDepositTransaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a cashback transaction which has been pending for 5 hours", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        const assetTransaction = await buildAssetTransaction({ owner: user.id });

        await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfHoursAgo(FRIDAY, 5)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a cashback transaction which has been pending for two work days", () => {
      let transaction: CashbackTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        const assetTransaction = await buildAssetTransaction({ owner: user.id });

        transaction = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                category: transaction.category,
                email: user.email,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a WH dividend transaction which has been pending for 5 hours", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfHoursAgo(FRIDAY, 5)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a WH dividend transaction which has been pending for two work days", () => {
      let transaction: WealthyhoodDividendTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        transaction = await buildWealthyhoodDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                category: transaction.category,
                email: user.email,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a direct debit asset transaction which has been pending for six work days", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        const deposit = (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 8),
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-id",
                status: "Pending"
              }
            }
          }
        })) as DepositCashTransactionDocument;

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          pendingDeposit: deposit,
          status: "Pending",
          wealthkernel: {
            status: "Pending"
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 8)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a direct debit asset transaction which has been pending for 15 work days", () => {
      let transaction: AssetTransactionDocument;
      let deposit: DepositCashTransactionDocument;
      let user: UserDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        deposit = (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateNWorkDaysAgo(FRIDAY, 14),
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-id",
                status: "Pending"
              }
            }
          }
        })) as DepositCashTransactionDocument;

        transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          pendingDeposit: deposit,
          status: "Pending",
          wealthkernel: {
            status: "Pending"
          },
          createdAt: DateUtil.getDateNWorkDaysAgo(FRIDAY, 15)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 2 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                email: user.email,
                category: transaction.category,
                consideration: transaction.consideration
              },
              {
                id: deposit.id,
                createdAt: deposit.createdAt,
                owner: deposit.owner.toString(),
                email: user.email,
                category: deposit.category,
                consideration: deposit.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a direct debit deposit transaction which has been pending for less than a day and is still unsubmitted", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfHoursAgo(FRIDAY, 6)
        })) as DepositCashTransactionDocument;
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a direct debit deposit transaction which has been pending for two work days and is still unsubmitted", () => {
      let transaction: DepositCashTransactionDocument;
      let user: UserDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        // Create unsubmitted direct debit deposit
        transaction = (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
        })) as DepositCashTransactionDocument;
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                email: user.email,
                category: transaction.category,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a direct debit deposit transaction which has been pending for nine work days and is still unsubmitted", () => {
      let transaction: DepositCashTransactionDocument;
      let user: UserDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        // Create unsubmitted direct debit deposit
        transaction = (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateNWorkDaysAgo(FRIDAY, 9)
        })) as DepositCashTransactionDocument;
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                email: user.email,
                category: transaction.category,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a direct debit deposit transaction which has been pending for six work days and is submitted", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 8),
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-id",
                status: "Pending"
              }
            }
          }
        })) as DepositCashTransactionDocument;
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a direct debit deposit transaction which has been pending for 14 work days", () => {
      let transaction: DepositCashTransactionDocument;
      let user: UserDocument;
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        transaction = (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateNWorkDaysAgo(FRIDAY, 14),
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-id",
                status: "Pending"
              }
            }
          }
        })) as DepositCashTransactionDocument;
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                email: user.email,
                category: transaction.category,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a deposit transaction which has been pending for 1 hour", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildDepositCashTransaction(
          {
            portfolio,
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              }
            },
            consideration: {
              currency: "GBP",
              amount: 1000
            },
            createdAt: DateUtil.getDateOfHoursAgo(FRIDAY, 1)
          },
          user,
          portfolio
        );
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a deposit transaction which has been pending for three work days", () => {
      let transaction: DepositCashTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        transaction = await buildDepositCashTransaction(
          {
            portfolio,
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              },
              truelayer: {
                version: "v3",
                status: "executed",
                id: faker.string.uuid()
              }
            },
            consideration: {
              currency: "GBP",
              amount: 1000
            },
            createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
          },
          user,
          portfolio
        );
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                owner: transaction.owner.toString(),
                createdAt: transaction.createdAt,
                email: user.email,
                category: transaction.category,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is an asset transaction which has been pending for three days but two of them are the weekend", () => {
      beforeEach(async () => {
        // Make the date a Monday
        const MONDAY = new Date("2022-06-27");
        Date.now = jest.fn(() => MONDAY.getTime());

        await buildAssetTransaction({
          portfolio: portfolio.id,
          wealthkernel: {
            status: "Pending"
          },
          createdAt: DateUtil.getDateOfDaysAgo(MONDAY, 3)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a rebalance transaction which has been pending for two work days", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-05T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());
        await buildRebalanceTransaction({
          owner: user.id,
          portfolio,
          rebalanceStatus: "PendingSell",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a rebalance transaction which has been pending for eight work days", () => {
      let transaction: RebalanceTransactionDocument;
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-05T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());
        transaction = await buildRebalanceTransaction({
          owner: user.id,
          portfolio,
          rebalanceStatus: "PendingSell",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 8)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                owner: transaction.owner.toString(),
                createdAt: transaction.createdAt,
                email: user.email,
                category: transaction.category,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a cashback linked to a direct debit card asset transaction which has been pending for eight work days", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        const user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        const deposit = (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 8),
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-id",
                status: "Pending"
              }
            }
          }
        })) as DepositCashTransactionDocument;

        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          pendingDeposit: deposit,
          status: "Pending",
          wealthkernel: {
            status: "Pending"
          },
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 8)
        });
        await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 8)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a direct debit asset transaction which has been pending for 14 work days", () => {
      let cashbackTransaction: CashbackTransactionDocument;
      let assetTransaction: AssetTransactionDocument;
      let deposit: DepositCashTransactionDocument;
      let user: UserDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        user = await buildUser();

        // User has a portfolio without holdings and cash
        const portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: "WK-PORTFOLIO-ID", status: "Active" } },
          holdings: [],
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
        });

        const automation = await buildTopUpAutomation({
          owner: user.id,
          portfolio: portfolio.id,
          active: true
        });

        deposit = (await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          createdAt: DateUtil.getDateNWorkDaysAgo(FRIDAY, 15),
          directDebit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: "some-id",
                status: "Pending"
              }
            }
          }
        })) as DepositCashTransactionDocument;

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAutomation: automation.id,
          pendingDeposit: deposit,
          status: "Pending",
          wealthkernel: {
            status: "Pending"
          },
          createdAt: DateUtil.getDateNWorkDaysAgo(FRIDAY, 16)
        });
        cashbackTransaction = await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransaction.id,
          status: "Pending",
          createdAt: DateUtil.getDateNWorkDaysAgo(FRIDAY, 17)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).toHaveBeenCalledWith("There are 3 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: cashbackTransaction.id,
                createdAt: cashbackTransaction.createdAt,
                owner: cashbackTransaction.owner.toString(),
                email: user.email,
                category: cashbackTransaction.category,
                consideration: cashbackTransaction.consideration
              },
              {
                id: assetTransaction.id,
                createdAt: assetTransaction.createdAt,
                owner: assetTransaction.owner.toString(),
                email: user.email,
                category: assetTransaction.category,
                consideration: assetTransaction.consideration
              },
              {
                id: deposit.id,
                createdAt: deposit.createdAt,
                owner: deposit.owner.toString(),
                email: user.email,
                category: deposit.category,
                consideration: deposit.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a settled savings topup transaction which was created three days ago", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an savings topup transaction which has been pending for one work day", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 1)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an savings topup transaction which has been pending for three work days", () => {
      let transaction: SavingsTopupTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        transaction = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
        });
      });
      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                category: transaction.category,
                email: user.email,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is a settled savings withdrawal transaction which was created four days ago", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 4)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an savings withdrawal transaction which has been pending for one work day", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 1)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an savings withdrawal transaction which has been pending for four work days", () => {
      let transaction: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        transaction = await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 4)
        });
      });
      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                category: transaction.category,
                email: user.email,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is an savings withdrawal transaction which has been pending topup for four work days", () => {
      let transaction: SavingsWithdrawalTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        transaction = await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingTopUp",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 4)
        });
      });
      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                category: transaction.category,
                email: user.email,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });

    describe("when there is an savings dividend transaction which has been pending for one work day", () => {
      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 1)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an savings dividend transaction which has been pending for three work days", () => {
      let transaction: SavingsDividendTransactionDocument;

      beforeEach(async () => {
        const FRIDAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => FRIDAY.valueOf());

        transaction = await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          createdAt: DateUtil.getDateOfDaysAgo(FRIDAY, 3)
        });
      });
      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantTransactions();
        expect(logger.error).toHaveBeenCalledWith("There are 1 transactions that are stagnant!", {
          method: "checkStagnantTransactions",
          module: "DataVerificationService",
          data: {
            allTransactions: [
              {
                id: transaction.id,
                createdAt: transaction.createdAt,
                owner: transaction.owner.toString(),
                category: transaction.category,
                email: user.email,
                consideration: transaction.consideration
              }
            ]
          }
        });
      });
    });
  });

  describe("checkStagnantRewards", () => {
    describe("when there is a reward that is settled", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const referrer = await buildUser();
        const referral = await buildUser();

        await buildReward({
          status: "Settled",
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 5)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantRewards();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a reward that is pending but not accepted", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const referrer = await buildUser();
        const referral = await buildUser();

        await buildReward({
          status: "Pending",
          accepted: false,
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 5)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantRewards();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an accepted reward that is pending for less than 2 work days", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const referrer = await buildUser();
        const referral = await buildUser();

        await buildReward({
          status: "Pending",
          accepted: true,
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantRewards();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is an accepted reward that is pending for more than 2 work days", () => {
      let reward: RewardDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const referrer = await buildUser();
        const referral = await buildUser();

        reward = await buildReward({
          status: "Pending",
          accepted: true,
          referrer: referrer.id,
          referral: referral.id,
          targetUser: referrer.id,
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 5)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantRewards();

        expect(logger.error).toHaveBeenCalledWith("There are 1 rewards that are stagnant!", {
          method: "checkStagnantRewards",
          module: "DataVerificationService",
          data: {
            rewards: [expect.objectContaining({ id: reward.id })]
          }
        });
      });
    });
  });

  describe("checkUsersWithMissingSummaries", () => {
    describe("when a user has no summaries", () => {
      const FRIDAY = new Date("2022-08-05T11:00:00Z");

      beforeEach(async () => {
        Date.now = jest.fn(() => FRIDAY.valueOf());

        await buildUser();
      });

      it("should not log any errors", async () => {
        await DataVerificationService.checkUsersWithMissingSummaries();

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith("🥳 No missing summaries found! 🥳!", {
          module: "DataVerificationService",
          method: "checkUsersWithMissingSummaries"
        });
      });
    });

    describe("when a user all summaries up to the previous work day", () => {
      const FRIDAY = new Date("2022-08-05T11:00:00Z");

      let user: UserDocument;

      beforeEach(async () => {
        Date.now = jest.fn(() => FRIDAY.valueOf());

        user = await buildUser();

        // Create daily summary for Thursday (previous work day from Friday)
        await buildDailySummarySnapshot({
          metadata: { owner: user.id },
          date: DateUtil.getDateOfDaysAgo(FRIDAY, 1)
        });
      });

      it("should not log any errors", async () => {
        await DataVerificationService.checkUsersWithMissingSummaries();

        expect(logger.error).not.toHaveBeenCalled();
        expect(logger.info).toHaveBeenCalledWith("🥳 No missing summaries found! 🥳!", {
          module: "DataVerificationService",
          method: "checkUsersWithMissingSummaries"
        });
      });
    });

    describe("when a user has a missing summary for the previous work day", () => {
      const FRIDAY = new Date("2022-08-05T11:00:00Z");

      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();

        Date.now = jest.fn(() => FRIDAY.valueOf());

        // Create summary for Wednesday (2 days before Friday)
        await buildDailySummarySnapshot({
          metadata: { owner: user.id },
          date: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
        });
      });

      it("should log an error", async () => {
        await DataVerificationService.checkUsersWithMissingSummaries();
        expect(logger.error).toHaveBeenCalledWith("1 missing summaries found!", {
          module: "DataVerificationService",
          method: "checkUsersWithMissingSummaries",
          data: {
            entriesForMissingSummaries: [
              {
                userId: user.id,
                lastSummaryDate: DateUtil.getDateOfDaysAgo(FRIDAY, 2)
              }
            ]
          }
        });
      });
    });
  });

  describe("checkStagnantAutomations", () => {
    describe("when there is a top-up automation that is inactive", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        await buildTopUpAutomation({
          owner: user.id,
          active: false,
          dayOfMonth: 5
        });
      });

      it("should NOT log a validation error", async () => {
        await DataVerificationService.checkStagnantAutomations();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a top-up automation that is active but is not yet initialised", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        await buildTopUpAutomation({
          owner: user.id,
          active: true,
          dayOfMonth: 5,
          initialiseAt: DateUtil.getDateAfterNdays(TODAY, 5)
        });
      });

      it("should NOT log a validation error", async () => {
        await DataVerificationService.checkStagnantAutomations();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a top-up automation that has day of month set to 6 work days from now", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        await buildTopUpAutomation({
          owner: user.id,
          active: true,
          dayOfMonth: 11
        });
      });

      it("should NOT log a validation error", async () => {
        await DataVerificationService.checkStagnantAutomations();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a top-up automation that has day of month set to tomorrow but has no transactions linked to it", () => {
      let user: UserDocument;
      let automation: AutomationDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        automation = await buildTopUpAutomation({
          owner: user.id,
          active: true,
          dayOfMonth: 5
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantAutomations();

        expect(logger.error).toHaveBeenCalledWith(
          "There are 1 automations that are stagnant!",
          expect.objectContaining({
            data: {
              automations: [expect.objectContaining({ email: user.email, id: automation.id, owner: user.id })]
            }
          })
        );
      });
    });

    describe("when there is a savings top-up automation that has day of month set to tomorrow but has no transactions linked to it", () => {
      let user: UserDocument;
      let automation: AutomationDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        automation = await buildSavingsTopUpAutomation({
          owner: user.id,
          active: true,
          dayOfMonth: 5
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantAutomations();

        expect(logger.error).toHaveBeenCalledWith(
          "There are 1 automations that are stagnant!",
          expect.objectContaining({
            data: {
              automations: [expect.objectContaining({ email: user.email, id: automation.id, owner: user.id })]
            }
          })
        );
      });
    });

    describe("when there is a top-up automation that has day of month set to tomorrow but has valid transactions linked to it", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const automation = await buildTopUpAutomation({
          owner: user.id,
          active: true,
          dayOfMonth: 5
        });

        await Promise.all([
          buildDepositCashTransaction({
            owner: user.id,
            linkedAutomation: automation.id
          }),
          buildAssetTransaction({
            owner: user.id,
            linkedAutomation: automation.id
          })
        ]);
      });

      it("should NOT log a validation error", async () => {
        await DataVerificationService.checkStagnantAutomations();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a savings top-up automation that has day of month set to tomorrow but has valid transactions linked to it", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        const automation = await buildSavingsTopUpAutomation({
          owner: user.id,
          active: true,
          dayOfMonth: 5
        });

        await Promise.all([
          buildDepositCashTransaction({
            owner: user.id,
            linkedAutomation: automation.id
          }),
          buildSavingsTopup({
            owner: user.id,
            linkedAutomation: automation.id
          })
        ]);
      });

      it("should NOT log a validation error", async () => {
        await DataVerificationService.checkStagnantAutomations();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });
  });

  describe("checkStagnantUserDataRequests", () => {
    describe("when there is a user data request that is completed", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        await buildUserDataRequest({
          requestType: "disassociation",
          owner: user.id,
          status: "Completed",
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 40)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantUserDataRequests();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a user data request that is not completed and has been created < 35 days ago", () => {
      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        await buildUserDataRequest({
          requestType: "disassociation",
          owner: user.id,
          status: "Created",
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 30)
        });
      });

      it("should not log a validation error", async () => {
        await DataVerificationService.checkStagnantUserDataRequests();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a user data request that is not completed and has been created > 35 days ago", () => {
      let userDataRequest: UserDataRequestDocument;

      beforeEach(async () => {
        const TODAY = new Date("2022-08-04T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const user = await buildUser();
        userDataRequest = await buildUserDataRequest({
          requestType: "disassociation",
          owner: user.id,
          status: "Created",
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 40)
        });
      });

      it("should log a validation error", async () => {
        await DataVerificationService.checkStagnantUserDataRequests();

        expect(logger.error).toHaveBeenCalledWith("There are 1 user data requests that are stagnant!", {
          method: "checkStagnantUserDataRequests",
          module: "DataVerificationService",
          data: {
            stagnantUserDataRequests: [expect.objectContaining({ id: userDataRequest.id })]
          }
        });
      });
    });
  });

  describe("checkWealthyhoodCashBalance", () => {
    describe("when our balance is less than £500", () => {
      const lowBalanceAmount = 200;

      beforeEach(async () => {
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([
          {
            value: {
              currency: CurrencyEnum.GBP,
              amount: lowBalanceAmount
            }
          }
        ]);
      });

      it("should log an error", async () => {
        await DataVerificationService.checkWealthyhoodCashBalance();

        expect(logger.error).toHaveBeenCalledWith(
          `💸 Our cash balance is currently ${lowBalanceAmount}, we should top-up ASAP...`,
          {
            module: "DataVerificationService",
            method: "checkWealthyhoodCashBalance"
          }
        );
      });
    });

    describe("when our balance is more than £500", () => {
      beforeEach(async () => {
        jest.spyOn(WealthkernelService.UKInstance, "listCashBalances").mockResolvedValue([
          {
            value: {
              currency: CurrencyEnum.GBP,
              amount: 550
            }
          }
        ]);
      });

      it("should log an error", async () => {
        await DataVerificationService.checkWealthyhoodCashBalance();

        expect(logger.error).not.toHaveBeenCalled();
      });
    });
  });

  describe("checkSubscriptionNextChargeDate", () => {
    describe("when there are no subscription with invalid nextChargeAt field", () => {
      beforeEach(async () => {
        const TODAY = new Date("2023-12-18T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const userWithActiveSubscription = await buildUser();
        await buildSubscription({
          owner: userWithActiveSubscription.id,
          active: true,
          nextChargeAt: DateUtil.getDateAfterNdays(TODAY, 5)
        });

        const userWithInactiveSubscription = await buildUser();
        await buildSubscription({
          owner: userWithInactiveSubscription.id,
          active: false,
          nextChargeAt: DateUtil.getDateOfDaysAgo(TODAY, 5)
        });

        await DataVerificationService.checkSubscriptionNextChargeDate();
      });

      it("should not log any error messages", () => {
        expect(logger.error).not.toHaveBeenCalled();
      });

      it("should log one info message", () => {
        expect(logger.info).toHaveBeenNthCalledWith(1, "🥳 No subscriptions have invalid nextChargeAt field!", {
          module: "DataVerificationService",
          method: "checkSubscriptionNextChargeDate"
        });
      });
    });

    describe("when there are subscriptions with invalid nextChargeAt field", () => {
      beforeEach(async () => {
        const TODAY = new Date("2023-12-18T11:00:00Z");
        Date.now = jest.fn(() => TODAY.valueOf());

        const userWithActiveSubscription = await buildUser();
        await buildSubscription({
          owner: userWithActiveSubscription.id,
          active: true,
          nextChargeAt: DateUtil.getDateOfDaysAgo(TODAY, 5)
        });

        const userWithInactiveSubscription = await buildUser();
        await buildSubscription({
          owner: userWithInactiveSubscription.id,
          active: true,
          nextChargeAt: DateUtil.getDateOfDaysAgo(TODAY, 5)
        });

        await DataVerificationService.checkSubscriptionNextChargeDate();
      });

      it("should log 3 error messages in total", async () => {
        expect(logger.error).toHaveBeenCalledTimes(3);
      });

      it("should log one error message per subscription", async () => {
        const subscriptions = await Subscription.find({});
        expect(logger.error).toHaveBeenCalledWith(
          `Found subscription ${subscriptions[0].id} with old nextChargeAt field`,
          {
            module: "DataVerificationService",
            method: "checkSubscriptionNextChargeDate",
            data: { subscription: subscriptions[0].id }
          }
        );
        expect(logger.error).toHaveBeenCalledWith(
          `Found subscription ${subscriptions[1].id} with old nextChargeAt field`,
          {
            module: "DataVerificationService",
            method: "checkSubscriptionNextChargeDate",
            data: { subscription: subscriptions[1].id }
          }
        );
      });

      it("should log one generic error message", () => {
        expect(logger.error).toHaveBeenCalledWith("⏱️ Found 2 subscriptions with invalid nextChargeAt field", {
          module: "DataVerificationService",
          method: "checkSubscriptionNextChargeDate"
        });
      });
    });
  });

  describe("checkStagnantBankAccounts", () => {
    const wkBankAccountId = "WK_BANK_ACCOUNT_ID";
    const FRIDAY = new Date("2022-08-04T11:00:00Z");

    describe("when there is a bank account which has been pending for 5 hours", () => {
      beforeEach(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(() => FRIDAY.valueOf());
        const owner = await buildUser();
        await buildBankAccount({
          owner: owner.id,
          providers: {
            wealthkernel: { id: wkBankAccountId, status: "Pending" }
          },
          createdAt: DateUtil.getDateOfHoursAgo(FRIDAY, 5)
        });
        await DataVerificationService.checkStagnantBankAccounts();
      });

      it("should not log a validation error", () => {
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a bank account which has been pending for 15 hours but it has already been deactivated", () => {
      beforeEach(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(() => FRIDAY.valueOf());
        const owner = await buildUser();

        await buildBankAccount({
          owner: owner.id,
          active: false,
          deactivationReason: DeactivationReasonEnum.BANK_NAME_MISMATCH,
          providers: {
            wealthkernel: { id: wkBankAccountId, status: "Pending" }
          },
          createdAt: DateUtil.getDateOfHoursAgo(FRIDAY, 15)
        });
        await DataVerificationService.checkStagnantBankAccounts();
      });

      it("should not log a validation error", () => {
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a bank account which has been pending for 15 hours", () => {
      let bankAccount: BankAccountDocument;

      beforeEach(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(() => FRIDAY.valueOf());
        const owner = await buildUser();
        bankAccount = await buildBankAccount({
          owner: owner.id,
          providers: {
            wealthkernel: { id: wkBankAccountId, status: "Pending" }
          },
          createdAt: DateUtil.getDateOfHoursAgo(FRIDAY, 15)
        });
        await DataVerificationService.checkStagnantBankAccounts();
      });

      it("should log a validation error", () => {
        expect(logger.error).toHaveBeenCalledWith("There are 1 bank accounts that are stagnant!", {
          method: "checkStagnantBankAccounts",
          module: "DataVerificationService",
          data: {
            allBankAccounts: [
              expect.objectContaining({
                id: bankAccount.id,
                createdAt: bankAccount.createdAt,
                owner: bankAccount.owner
              })
            ]
          }
        });
      });
    });
  });

  describe("checkUnpublishedFinimizeContentEntries", () => {
    describe("when there is a content entry that should have been published half an hour ago", () => {
      let contentEntry: ContentEntryDocument;

      beforeEach(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());

        contentEntry = await buildContentEntry({
          publishAt: DateUtil.getDateOfMinutesAgo(30),
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: DateUtil.getDateOfMinutesAgo(30)
            }
          }
        });

        await DataVerificationService.checkUnpublishedFinimizeContentEntries();
      });

      it("should NOT log a validation error", () => {
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a content entry that should be published 2 hours in the future", () => {
      let contentEntry: ContentEntryDocument;

      beforeEach(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());

        contentEntry = await buildContentEntry({
          publishAt: DateUtil.getDateOfMinutesFromNow(120),
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: DateUtil.getDateOfMinutesFromNow(120)
            }
          }
        });

        await DataVerificationService.checkUnpublishedFinimizeContentEntries();
      });

      it("should NOT log a validation error", () => {
        expect(logger.error).not.toHaveBeenCalled();
      });
    });

    describe("when there is a content entry that should have been published 2 days ago", () => {
      let contentEntry: ContentEntryDocument;

      beforeEach(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(() => new Date().getTime());

        contentEntry = await buildContentEntry({
          publishAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          providers: {
            finimize: {
              id: faker.string.uuid(),
              contentType: FinimizeContentTypeEnum.INSIGHT,
              publishedAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
            }
          }
        });

        await DataVerificationService.checkUnpublishedFinimizeContentEntries();
      });

      it("should log a validation error", () => {
        expect(logger.error).toHaveBeenCalledWith("There are 1 unpublished finimize content entries!", {
          method: "checkUnpublishedFinimizeContentEntries",
          module: "DataVerificationService",
          data: {
            allContentEntries: [
              expect.objectContaining({ id: contentEntry.id, publishAt: contentEntry.publishAt })
            ]
          }
        });
      });
    });
  });
});
