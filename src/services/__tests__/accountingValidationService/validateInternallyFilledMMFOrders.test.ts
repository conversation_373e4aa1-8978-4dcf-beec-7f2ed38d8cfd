import "jest";
import Decimal from "decimal.js";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { clearSqliteDb, createSqliteDb, connectDb, clearDb, closeDb } from "../../../tests/utils/db";
import {
  buildUser,
  buildPortfolio,
  buildOrder,
  buildSavingsTopup,
  buildSavingsWithdrawal
} from "../../../tests/utils/generateModels";
import { AccountingValidationService } from "../../accountingValidationService";
import AccountingLedgerStorageService from "../../../external-services/accountingLedgerStorageService";
import { LedgerAccounts } from "../../../types/accounting";
import { UserDocument } from "../../../models/User";
import { PortfolioDocument } from "../../../models/Portfolio";

describe("AccountingValidationService.validateInternallyFilledMMFOrders", () => {
  const TODAY = "2025-06-12";
  const FROM_DATE = "2025-06-01";

  beforeAll(async () => {
    Date.now = jest.fn(() => +new Date(TODAY));
    await connectDb("validateInternallyFilledMMFOrders");
    await createSqliteDb();
  });

  afterAll(async () => await closeDb());

  afterEach(async () => {
    await clearSqliteDb();
    await clearDb();
  });

  describe("validateInternallyFilledMMFOrders", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({
        residencyCountry: "GR",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      portfolio = await buildPortfolio({ owner: user.id });
    });

    it("should validate that internally filled MMF orders generate no ledger entries", async () => {
      const ORDER_AMOUNT_CENTS = 50000; // £500.00 in cents

      // Create topup and withdrawal transactions
      const savingsTopup = await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" }
      });
      const savingsWithdrawal = await buildSavingsWithdrawal({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" }
      });

      // Create internally filled orders
      await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "InternallyFilled",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      await buildOrder({
        transaction: savingsWithdrawal.id,
        side: "Sell",
        status: "InternallyFilled",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Run validation
      const result = await AccountingValidationService.validateInternallyFilledMMFOrders(FROM_DATE);

      // Verify validation passes
      expect(result.transactionType).toBe("mmf_internally_filled");
      expect(result.isValid).toBe(true);
      expect(result.dbTotalAmount).toBe(new Decimal(ORDER_AMOUNT_CENTS * 2).div(100).toNumber()); // 2 orders * 500 euros = 1000 euros
      expect(result.ledgerTotalAmount).toBe(0);
      expect(result.difference).toBe(0);
      expect(result.transactionCount).toBe(2);
      expect(result.ledgerEntryCount).toBe(0);
      expect(result.discrepancies).toBeUndefined();
    });

    it("should detect validation failure when internally filled orders have ledger entries", async () => {
      const ORDER_AMOUNT_CENTS = 30000; // £300.00 in cents
      const ORDER_AMOUNT_EUROS = new Decimal(ORDER_AMOUNT_CENTS).div(100).toNumber();

      // Create topup and withdrawal transactions
      const savingsTopup = await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" }
      });

      // Create internally filled order
      const internallyFilledOrder = await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "InternallyFilled",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Manually create ledger entries for this order (simulating a bug)
      await AccountingLedgerStorageService.addValidatedLedgerEntries([
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENT_DOMESTIC,
          side: "debit",
          amount: ORDER_AMOUNT_EUROS,
          reference_number: undefined,
          article_date: TODAY,
          description: `${user.id}|${internallyFilledOrder.id}|asset buy`,
          document_id: internallyFilledOrder.id,
          owner_id: user.id
        },
        {
          aa: 1,
          account_code: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS,
          side: "credit",
          amount: ORDER_AMOUNT_EUROS,
          reference_number: undefined,
          article_date: TODAY,
          description: `${user.id}|${internallyFilledOrder.id}|asset buy`,
          document_id: internallyFilledOrder.id,
          owner_id: user.id
        }
      ]);

      // Run validation
      const result = await AccountingValidationService.validateInternallyFilledMMFOrders(FROM_DATE);

      // Verify validation fails
      expect(result.transactionType).toBe("mmf_internally_filled");
      expect(result.isValid).toBe(false);
      expect(result.dbTotalAmount).toBe(ORDER_AMOUNT_EUROS);
      expect(result.ledgerTotalAmount).toBe(ORDER_AMOUNT_EUROS * 2); // 2 ledger entries
      expect(result.difference).toBe(ORDER_AMOUNT_EUROS * 2);
      expect(result.transactionCount).toBe(1);
      expect(result.ledgerEntryCount).toBe(2);
      expect(result.discrepancies).toBeDefined();
      expect(result.discrepancies!).toHaveLength(1);
      expect(result.discrepancies![0]).toEqual({
        transactionId: internallyFilledOrder.id,
        dbAmount: 0, // Internally filled orders should have 0 ledger impact
        ledgerAmount: ORDER_AMOUNT_EUROS * 2,
        difference: ORDER_AMOUNT_EUROS * 2
      });
    });

    it("should handle date filtering correctly", async () => {
      const ORDER_AMOUNT_CENTS = 25000; // £250.00 in cents
      const PAST_DATE = "2025-05-01";
      const FUTURE_DATE = "2025-07-01";

      // Create topup transaction
      const savingsTopup = await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: { amount: ORDER_AMOUNT_CENTS, currency: "EUR" }
      });

      // Create internally filled order with past date
      await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "InternallyFilled",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(PAST_DATE)
      });

      // Create internally filled order with current date
      await buildOrder({
        transaction: savingsTopup.id,
        side: "Buy",
        status: "InternallyFilled",
        consideration: {
          originalAmount: ORDER_AMOUNT_CENTS,
          amountSubmitted: ORDER_AMOUNT_CENTS,
          amount: ORDER_AMOUNT_CENTS,
          currency: "EUR"
        },
        fees: { fx: { amount: 0, currency: "EUR" }, realtimeExecution: { amount: 0, currency: "EUR" } },
        activeProviders: [],
        isin: "IE00B404XK09", // MMF ISIN
        filledAt: new Date(TODAY)
      });

      // Run validation from FROM_DATE - should only include the current date order
      const result = await AccountingValidationService.validateInternallyFilledMMFOrders(FROM_DATE);

      // Verify validation passes and only counts orders from FROM_DATE onwards
      expect(result.transactionType).toBe("mmf_internally_filled");
      expect(result.isValid).toBe(true);
      expect(result.transactionCount).toBe(1); // Only the current date order should be counted
      expect(result.dbTotalAmount).toBe(new Decimal(ORDER_AMOUNT_CENTS).div(100).toNumber()); // 250 euros

      // Run validation from FUTURE_DATE - should include no orders
      const futureResult = await AccountingValidationService.validateInternallyFilledMMFOrders(FUTURE_DATE);
      expect(futureResult.transactionCount).toBe(0);
      expect(futureResult.dbTotalAmount).toBe(0);
    });
  });
});
