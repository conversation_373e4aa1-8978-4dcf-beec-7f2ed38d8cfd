import { faker } from "@faker-js/faker";
import { Decimal } from "decimal.js";
import { entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { auth0ManagementClient } from "../../external-services/auth0ManagementService";
import logger from "../../external-services/loggerService";
import { PortfolioWrapperTypeEnum } from "../../external-services/wealthkernelService";
import { Automation, TopUpAutomationDTOInterface } from "../../models/Automation";
import { Account } from "../../models/Account";
import { Address } from "../../models/Address";
import { BankAccount } from "../../models/BankAccount";
import { DailyPortfolioTicker } from "../../models/DailyTicker";
import {
  DailySummarySnapshot,
  IndividualSentimentScoreComponentEnum,
  TotalSentimentScoreComponentEnum
} from "../../models/DailySummarySnapshot";
import { envIsProd } from "../../utils/environmentUtil";
import {
  buildAccount,
  buildAddress,
  buildAssetTransaction,
  buildBankAccount,
  buildDailyPortfolioTickersBulk,
  buildDepositCashTransaction,
  buildDividendTransaction,
  buildGift,
  buildIntraDayPortfolioTickersBulk,
  buildKycOperation,
  buildMandate,
  buildOrder,
  buildParticipant,
  buildPortfolio,
  buildRebalanceAutomation,
  buildReward,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSavingsTopUpAutomation,
  buildSubscription,
  buildTopUpAutomation,
  buildUserDTO,
  buildWallet
} from "./generateModels";
import { Gift, GiftDTOInterface } from "../../models/Gift";
import { KycOperation } from "../../models/KycOperation";
import { Mandate } from "../../models/Mandate";
import { Order } from "../../models/Order";
import { Participant } from "../../models/Participant";
import { PaymentMethod } from "../../models/PaymentMethod";
import { Portfolio, PortfolioDTOInterface } from "../../models/Portfolio";
import { ReferralCode } from "../../models/ReferralCode";
import { RewardInvitation } from "../../models/RewardInvitation";
import { Reward, RewardDTOInterface } from "../../models/Reward";
import { RiskAssessment } from "../../models/RiskAssessment";
import { Subscription } from "../../models/Subscription";
import { SavingsTopupTransactionDTOInterface, Transaction } from "../../models/Transaction";
import { DepositMethodEnum } from "../../types/transactions";
import { Auth0ObjectType, KycStatusEnum, User, UserDocument, UserInterface } from "../../models/User";
import { UserDataRequest } from "../../models/UserDataRequest";
import { ProviderEnum } from "../../configs/providersConfig";
import { DepositActionEnum } from "../../configs/depositsConfig";
import ConfigUtil from "../../utils/configUtil";
import { SavingsProduct } from "../../models/SavingsProduct";
import { PortfolioAllocationMethodEnum } from "../../services/portfolioService";

const { ASSET_CONFIG } = investmentUniverseConfig;

export enum UserStatusEnum {
  NEW = "NEW",
  VERIFIED = "VERIFIED",
  VERIFIED_WITH_PENDING_REWARD = "VERIFIED_WITH_PENDING_REWARD",
  VERIFIED_WITH_SUBSCRIPTION = "VERIFIED_WITH_SUBSCRIPTION",
  VERIFIED_WITH_GIFT = "VERIFIED_WITH_GIFT",
  VERIFIED_WITH_TARGET = "VERIFIED_WITH_TARGET",
  VERIFIED_WITH_BANK = "VERIFIED_WITH_BANK",
  VERIFIED_WITH_CASH = "VERIFIED_WITH_CASH",
  VERIFIED_WITH_CASH_GIFT = "VERIFIED_WITH_CASH_GIFT",
  VERIFIED_WITH_BANK_MANDATE = "VERIFIED_WITH_BANK_MANDATE",
  VERIFIED_WITH_AUTOMATIONS = "VERIFIED_WITH_AUTOMATIONS",
  INVESTED = "INVESTED",
  INVESTED_WITH_CASH = "INVESTED_WITH_CASH",
  INVESTED_WITH_CASH_NO_TARGET = "INVESTED_WITH_CASH_NO_TARGET",
  INVESTED_WITH_CASH_NO_BANK_ACCOUNT = "INVESTED_WITH_CASH_NO_BANK_ACCOUNT",
  INVESTED_WITH_REWARD = "INVESTED_WITH_REWARD",
  INVESTED_WITH_SAVINGS = "INVESTED_WITH_SAVINGS",
  FULLY_WITHDRAWN = "FULLY_WITHDRAWN",
  FULLY_WITHDRAWN_WITH_CASH = "FULLY_WITHDRAWN_WITH_CASH"
}

type UserRelationsType = {
  portfolio?: Partial<PortfolioDTOInterface>;
  transactions?: any[];
  subscription?: any;
  automations?: any[];
  rewards?: any[];
  gift?: any;
};

/**
 * @classdesc A builder that creates user documents in any stage of the user
 * funnel and with any type of associated data/document.
 *
 * @example How to HTTP:
 *
 /test/users

 {
  email: "<EMAIL>",
  state: "INVESTED_WITH_SAVINGS",
  props: {
    residencyCountry: "GB",
    firstName: "Kostas"
  },
  relations: {
    portfolio: {
      state: "INVESTED",
      props: {
        cash: {
          GBP: { available: 100, reserved: 0, settled: 100 }
        }
      }
    },
    transactions: [
      {
        state: "DEPOSIT_WITH_MINIMUM_AMOUNT_COMPLETED",
        props: {
          category: "Deposit",
          amount: 100,
          currency: "GBP"
        }
      }
    ]
  }
}
 *
 * @example How to use:
 *
 * await new UserBuilder()
 *   .build("<EMAIL>", UserStatusEnum.INVESTED)
 *   .withPortfolio()
 *   .withTransactions()
 *   .withSubscription()
 *   .withAutomations()
 *   .withReward()
 *   .withGift()
 *   .finish()
 *
 * NOTE:
 * The above examples indicate how the public methods can be used to add
 * data linked to the created user. However, it is not required to use all
 * those methods. If the goal is to create an invested user without any other
 * requirements, the class can be called as
 *
 * await new UserBuilder()
 *   .build("<EMAIL>", UserStatusEnum.INVESTED)
 *   .finish()
 *
 */
export default class UserBuilder {
  private _taskQueue: any[] = [];
  private _user: UserDocument;
  private _data: {
    portfolioTickerData: Record<string, number>; // date string -> portfolio value
  } = {
    portfolioTickerData: {}
  };

  constructor() {
    if (envIsProd()) {
      throw new Error("UserBuilder is not intended for prod use.");
    }
  }

  public get user(): UserDocument {
    return this._user;
  }

  // ***************
  // PUBLIC METHODS
  // ***************
  public build({
    email,
    status,
    props,
    relations // eslint-disable-line @typescript-eslint/no-unused-vars
  }: {
    email: string;
    status: UserStatusEnum;
    props?: Partial<UserInterface>; // User properties
    relations?: UserRelationsType; // Related items properties (currently unused)
  }): UserBuilder {
    if (!email) {
      throw new Error("Email cannot be empty");
    }

    // Clean up any user data before creating the user
    this._chain(async () => await this.reset(email));

    if (status === UserStatusEnum.NEW) {
      return this._createNewUser(email);
    } else if (status === UserStatusEnum.VERIFIED) {
      return this._createVerifiedUser(email, props);
    } else if (status === UserStatusEnum.VERIFIED_WITH_SUBSCRIPTION) {
      return this._createVerifiedUser(email, props).withSubscription();
    } else if (status === UserStatusEnum.VERIFIED_WITH_GIFT) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio({
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 40 },
            { assetCommonId: "equities_microsoft", percentage: 30 },
            { assetCommonId: "equities_us", percentage: 30 }
          ]
        })
        .withGift();
    } else if (status === UserStatusEnum.VERIFIED_WITH_PENDING_REWARD) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio()
        .withReward({
          status: "Pending",
          hasViewedAppModal: false,
          accepted: null,
          unrestrictedAt: null,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        });
    } else if (status === UserStatusEnum.VERIFIED_WITH_TARGET) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio({
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 40 },
            { assetCommonId: "equities_microsoft", percentage: 30 },
            { assetCommonId: "equities_us", percentage: 30 }
          ]
        });
    } else if (status === UserStatusEnum.VERIFIED_WITH_BANK) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio({
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 40 },
            { assetCommonId: "equities_microsoft", percentage: 30 },
            { assetCommonId: "equities_us", percentage: 30 }
          ]
        })
        .withBankAccount();
    } else if (status === UserStatusEnum.VERIFIED_WITH_CASH) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio({
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 40 },
            { assetCommonId: "equities_microsoft", percentage: 30 },
            { assetCommonId: "equities_us", percentage: 30 }
          ],
          cash: {
            GBP: { available: 1000, reserved: 0, settled: 1000 },
            EUR: { available: 1000, reserved: 0, settled: 1000 },
            USD: { available: 1000, reserved: 0, settled: 1000 }
          }
        })
        .withBankAccount();
    } else if (status === UserStatusEnum.VERIFIED_WITH_CASH_GIFT) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio({
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 40 },
            { assetCommonId: "equities_microsoft", percentage: 30 },
            { assetCommonId: "equities_us", percentage: 30 }
          ],
          cash: {
            GBP: { available: 1000, reserved: 0, settled: 1000 },
            EUR: { available: 1000, reserved: 0, settled: 1000 },
            USD: { available: 1000, reserved: 0, settled: 1000 }
          }
        })
        .withBankAccount()
        .withGift();
    } else if (status === UserStatusEnum.VERIFIED_WITH_BANK_MANDATE) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio({
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 40 },
            { assetCommonId: "equities_microsoft", percentage: 30 },
            { assetCommonId: "equities_us", percentage: 30 }
          ]
        })
        .withBankAccount()
        .withMandate();
    } else if (status === UserStatusEnum.VERIFIED_WITH_AUTOMATIONS) {
      return this._createVerifiedUser(email, props)
        .withSubscription()
        .withPortfolio({
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 40 },
            { assetCommonId: "equities_microsoft", percentage: 30 },
            { assetCommonId: "equities_us", percentage: 30 }
          ]
        })
        .withBankAccount()
        .withMandate()
        .withInvestmentTopUpAutomation({ allocationMethod: PortfolioAllocationMethodEnum.TARGET_ALLOCATION })
        .withRebalanceAutomation()
        .withSavingsTopUpAutomation();
    } else if (status === UserStatusEnum.INVESTED) {
      return this._createInvestedUser(email, props).withBankAccount();
    } else if (status === UserStatusEnum.INVESTED_WITH_CASH) {
      return this._createInvestedUser(email, props)
        .withBankAccount()
        .withPortfolio({
          cash: {
            GBP: { available: 1000, reserved: 0, settled: 1000 },
            EUR: { available: 1000, reserved: 0, settled: 1000 },
            USD: { available: 1000, reserved: 0, settled: 1000 }
          }
        });
    } else if (status === UserStatusEnum.INVESTED_WITH_CASH_NO_BANK_ACCOUNT) {
      return this._createInvestedUser(email, props).withPortfolio({
        cash: {
          GBP: { available: 1000, reserved: 0, settled: 1000 },
          EUR: { available: 1000, reserved: 0, settled: 1000 },
          USD: { available: 1000, reserved: 0, settled: 1000 }
        }
      });
    } else if (status === UserStatusEnum.INVESTED_WITH_CASH_NO_TARGET) {
      return this._createInvestedUser(email, props)
        .withBankAccount()
        .withPortfolio({
          cash: {
            GBP: { available: 1000, reserved: 0, settled: 1000 },
            EUR: { available: 1000, reserved: 0, settled: 1000 },
            USD: { available: 1000, reserved: 0, settled: 1000 }
          },
          initialHoldingsAllocation: []
        });
    } else if (status === UserStatusEnum.INVESTED_WITH_REWARD) {
      return this._createInvestedUser(email, props).withBankAccount().withReward();
    } else if (status === UserStatusEnum.INVESTED_WITH_SAVINGS) {
      return this._createInvestedUser(email, props).withBankAccount().withPortfolio().withSavings();
    } else if (status === UserStatusEnum.FULLY_WITHDRAWN) {
      return this._createInvestedUser(email, props).withBankAccount().withPortfolio({
        holdings: []
      });
    } else if (status === UserStatusEnum.FULLY_WITHDRAWN_WITH_CASH) {
      return this._createInvestedUser(email, props)
        .withBankAccount()
        .withPortfolio({
          holdings: [],
          cash: {
            GBP: { available: 1000, reserved: 0, settled: 1000 },
            EUR: { available: 1000, reserved: 0, settled: 1000 },
            USD: { available: 1000, reserved: 0, settled: 1000 }
          }
        });
    }
    throw new Error(`${status} is not a valid status`);
  }

  public async finish(): Promise<UserBuilder> {
    for (let i = 0; i < this._taskQueue.length; i++) {
      const taskFn = this._taskQueue[i];
      try {
        await taskFn();
      } catch (err) {
        logger.error("UserBuilder failed to finish", {
          module: "UserBuilder",
          method: "finish",
          data: { error: err, user: this._user?.email }
        });
      }
    }
    return this;
  }

  public withAccount(): UserBuilder {
    return this._chain(async () => {
      await buildAccount({
        owner: this._user.id,
        wrapperType: PortfolioWrapperTypeEnum.GIA,
        activeProviders: [ProviderEnum.WEALTHKERNEL],
        providers: {
          [ProviderEnum.WEALTHKERNEL]: {
            id: faker.string.uuid(), // TODO: fetch from WK
            status: "Active"
          }
        }
      });
    });
  }

  public withAddress(): UserBuilder {
    return this._chain(async () => {
      await buildAddress({ owner: this._user.id });
    });
  }

  public withBankAccount(): UserBuilder {
    return this._chain(async () => {
      await buildBankAccount({
        owner: this._user.id,
        providers: { truelayer: { bankId: "mock" }, wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
    });
  }

  public withMandate(): UserBuilder {
    return this._chain(async () => {
      const bankAccounts = await BankAccount.find({ owner: this._user.id });

      for (const bankAccount of bankAccounts) {
        await buildMandate({
          owner: this._user.id,
          bankAccount: bankAccount.id,
          category: "Top-Up",
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            [ProviderEnum.WEALTHKERNEL]: {
              id: faker.string.uuid(),
              status: "Active"
            }
          }
        });
      }
    });
  }

  public withInvestmentTopUpAutomation(automationProps?: Partial<TopUpAutomationDTOInterface>): UserBuilder {
    return this._chain(async () => {
      const portfolio = await Portfolio.findOne({ owner: this._user.id });
      const mandate = await Mandate.findOne({ owner: this._user.id });
      await buildTopUpAutomation({
        owner: this._user.id,
        portfolio: portfolio.id,
        mandate: mandate.id,
        ...(automationProps || {})
      });
    });
  }

  public withRebalanceAutomation(): UserBuilder {
    return this._chain(async () => {
      const portfolio = await Portfolio.findOne({ owner: this._user.id });
      await buildRebalanceAutomation({ owner: this._user.id, portfolio: portfolio.id });
    });
  }

  public withSavingsTopUpAutomation(): UserBuilder {
    return this._chain(async () => {
      const portfolio = await Portfolio.findOne({ owner: this._user.id });
      const mandate = await Mandate.findOne({ owner: this._user.id });
      await buildSavingsTopUpAutomation({ owner: this._user.id, portfolio: portfolio.id, mandate: mandate.id });
    });
  }

  public withDailySummaries(): UserBuilder {
    return this._chain(async () => {
      const ASSET_ID: investmentUniverseConfig.AssetType = "equities_apple";

      // Get the portfolio for the user
      const portfolio = await Portfolio.findOne({ owner: this._user.id });
      const currency = portfolio.currency;

      // Get stored ticker data dates and prices
      const portfolioTickerDataDates = Object.keys(this._data.portfolioTickerData).sort();

      // Generate the daily summaries in bulk
      const dailySummaries = portfolioTickerDataDates.map((dateStr) => {
        const date = new Date(dateStr);
        const portfolioPrice = this._data.portfolioTickerData[dateStr];
        const previousDateStr = portfolioTickerDataDates[portfolioTickerDataDates.indexOf(dateStr) - 1];
        const previousPortfolioPrice = previousDateStr
          ? this._data.portfolioTickerData[previousDateStr]
          : portfolioPrice * 0.99;
        const dailyReturn = (portfolioPrice - previousPortfolioPrice) / previousPortfolioPrice;

        // Convert price from cents to whole currency
        const displayPrice = Math.round(portfolioPrice / 100);

        return {
          metadata: {
            owner: this._user.id
          },
          date,
          sentimentScore: {
            [TotalSentimentScoreComponentEnum.TOTAL]: faker.number.float({ min: 0, max: 1 }),
            [IndividualSentimentScoreComponentEnum.NEWS]: faker.number.float({ min: 0, max: 1 }),
            [IndividualSentimentScoreComponentEnum.ANALYST]: faker.number.float({ min: 0, max: 1 }),
            [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: faker.number.float({ min: 0, max: 1 })
          },
          portfolio: {
            cash: {
              value: { amount: faker.number.int({ min: 1000, max: 2000 }), currency }
            },
            holdings: {
              value: { amount: displayPrice, currency },
              dailyUpBy: Math.round(portfolioPrice * dailyReturn) / 100,
              dailyReturnPercentage: dailyReturn,
              assets: [
                {
                  assetId: ASSET_ID,
                  quantity: faker.number.float({ min: 1, max: 10 }),
                  latestPrice: { amount: displayPrice / 10, currency },
                  holdingWeightPercentage: 1,
                  dailyReturnPercentage: dailyReturn
                }
              ]
            },
            savings: {
              value: { amount: faker.number.int({ min: 1000, max: 2000 }), currency }
            },
            total: {
              value: {
                amount: displayPrice + faker.number.int({ min: 2000, max: 4000 }),
                currency
              }
            }
          }
        };
      });

      // Insert all daily summaries in bulk
      await DailySummarySnapshot.insertMany(dailySummaries);
    });
  }

  public withKycOperation(): UserBuilder {
    // For now the implementation has been done for successful KYC operation only.
    return this._chain(async () => {
      await buildKycOperation({
        owner: this.user.id,
        activeProviders: [ProviderEnum.SUMSUB],
        status: "Passed",
        providers: {
          [ProviderEnum.SUMSUB]: {
            id: faker.string.uuid(),
            submittedAt: new Date(),
            status: "completed",
            decision: "GREEN"
          }
        }
      });
    });
  }

  public withParticipant(): UserBuilder {
    return this._chain(async () => {
      await buildParticipant({ email: this._user.email });
    });
  }

  public withWallet(): UserBuilder {
    return this._chain(async () => {
      if (this._user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
        await buildWallet({
          owner: this._user.id,
          iban: faker.finance.iban(),
          providers: {
            devengo: {
              id: faker.string.uuid(),
              status: "active"
            }
          }
        });
      }
    });
  }

  public withPortfolio(portfolioProps?: Partial<PortfolioDTOInterface>): UserBuilder {
    return this._chain(async () => {
      const accounts = await Account.find({ owner: this._user.id });
      const portfolios = await Portfolio.find({ owner: this._user.id });
      if (portfolios.length === 0) {
        const currency = ConfigUtil.getDefaultUserCurrency(this._user.residencyCountry);

        await buildPortfolio({
          owner: this._user.id,
          account: accounts[0].id,
          providers: {
            [ProviderEnum.WEALTHKERNEL]: {
              id: faker.string.uuid(), // TODO: fetch from wealthkernel
              status: "Active"
            }
          },
          currency,
          ...(portfolioProps || {})
        });
      } else {
        await Portfolio.findOneAndUpdate(
          { owner: this._user.id },
          {
            ...(portfolioProps || {})
          }
        );
      }
    });
  }

  public withSubscription(): UserBuilder {
    return this._chain(async () => {
      await buildSubscription({
        active: true,
        owner: this._user.id,
        category: "FeeBasedSubscription",
        price: "free_monthly"
      });
    });
  }

  public withInvestment(): UserBuilder {
    return this._chain(async () => {
      const currency = this._user.currency;
      const INVESTMENT_AMOUNT = 150; // Default investment amount in EUR/GBP
      const FX_FEE = 0.01; // FX fee in EUR/GBP
      const COMMISSION_FEE = 1; // Commission fee in EUR/GBP
      const STOCK_ID = "equities_apple";
      const ETF_ID = "equities_us";
      const ETF_ID_ΜΙΝ_QUANTITY = "equities_uk";

      // Fetch the portfolio
      const portfolio = await Portfolio.findOne({ owner: this._user.id });

      // Create a deposit transaction
      const deposit = await buildDepositCashTransaction({
        owner: this._user.id,
        depositMethod: DepositMethodEnum.OPEN_BANKING,
        depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
        portfolio: portfolio.id,
        status: "Settled",
        settledAt: new Date(),
        activeProviders: [ProviderEnum.WEALTHKERNEL, ProviderEnum.TRUELAYER],
        providers: {
          [ProviderEnum.WEALTHKERNEL]: {
            id: faker.string.uuid(),
            status: "Settled"
          },
          [ProviderEnum.TRUELAYER]: {
            id: faker.string.uuid(),
            status: "executed",
            version: "v3"
          }
        },
        consideration: {
          currency,
          amount: INVESTMENT_AMOUNT * 100
        }
      });

      // Create an asset transaction linked to the deposit
      const assetTransaction = await buildAssetTransaction({
        owner: this._user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "buy",
        status: "Settled",
        pendingDeposit: deposit.id,
        consideration: {
          currency,
          amount: INVESTMENT_AMOUNT * 100 - FX_FEE * 2 * 100 - COMMISSION_FEE * 100
        },
        fees: {
          fx: {
            amount: FX_FEE * 2,
            currency: currency
          },
          commission: {
            amount: COMMISSION_FEE,
            currency: currency
          }
        }
      });

      // Create stock order linked to the asset transaction
      const stockOrder = await buildOrder({
        transaction: assetTransaction.id,
        status: "Settled",
        side: "Buy",
        isin: ASSET_CONFIG[STOCK_ID].isin,
        filledAt: new Date(),
        quantity: 0.8,
        exchangeRate: 1.3,
        unitPrice: {
          amount: 100,
          currency: "USD"
        },
        consideration: {
          originalAmount: INVESTMENT_AMOUNT * 70,
          amountSubmitted: INVESTMENT_AMOUNT * 70 - FX_FEE * 100,
          amount: INVESTMENT_AMOUNT * 70 - FX_FEE * 100,
          currency
        },
        fees: {
          fx: {
            amount: FX_FEE,
            currency: currency
          }
        }
      });

      // Create ETF order linked to the asset transaction
      const etfOrder = await buildOrder({
        transaction: assetTransaction.id,
        status: "Settled",
        side: "Buy",
        isin: ASSET_CONFIG[ETF_ID].isin,
        filledAt: new Date(),
        quantity: 0.4,
        exchangeRate: currency === "GBP" ? 1 : 0.9,
        unitPrice: {
          amount: 100,
          currency: "GBP"
        },
        consideration: {
          originalAmount: INVESTMENT_AMOUNT * 30,
          amountSubmitted: INVESTMENT_AMOUNT * 30 - FX_FEE * 100 - COMMISSION_FEE * 100,
          amount: INVESTMENT_AMOUNT * 30,
          currency
        },
        fees: {
          fx: {
            amount: FX_FEE,
            currency: currency
          },
          commission: {
            amount: COMMISSION_FEE,
            currency: currency
          }
        }
      });

      assetTransaction.orders = [stockOrder, etfOrder];
      await assetTransaction.save();

      buildDividendTransaction({
        owner: this._user.id,
        portfolio: portfolio.id,
        asset: STOCK_ID,
        consideration: {
          amount: 100,
          currency
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        }
      });

      // Update portfolio holdings with the bought assets
      await Portfolio.findByIdAndUpdate(portfolio.id, {
        $set: {
          holdings: [
            {
              assetCommonId: STOCK_ID,
              quantity: stockOrder.quantity
            },
            {
              assetCommonId: ETF_ID,
              quantity: etfOrder.quantity
            },
            {
              assetCommonId: ETF_ID_ΜΙΝ_QUANTITY,
              quantity: 0.0002 // we want to achieve a value of less than 1
            }
          ]
        }
      });
    });
  }

  public withReward(props?: Partial<RewardDTOInterface>): UserBuilder {
    return this._chain(async () => {
      const currency = this._user.currency;
      const rewardDefaults: Partial<RewardDTOInterface> = {
        asset: "equities_sony" as investmentUniverseConfig.AssetType,
        quantity: 0.5,
        exchangeRate: 1.3,
        unitPrice: {
          amount: 100,
          currency: "USD"
        },
        status: "Settled",
        accepted: true,
        consideration: {
          currency,
          amount: 500, // 5 GBP in cents
          bonusAmount: 600, // 6 GBP in cents (amount + fees)
          orderAmount: 500 // 5 GBP in cents (same as amount for now)
        },
        fees: {
          fx: { amount: 1, currency }, // 1 GBP/EUR (stored in whole currency)
          commission: { amount: 0, currency },
          executionSpread: { amount: 0, currency }
        }
      };

      // Fetch the portfolio
      const portfolio = await Portfolio.findOne({ owner: this._user.id });

      // Create an reward
      const reward = await buildReward({
        targetUser: this._user.id,
        referral: this._user.id,
        ...rewardDefaults,
        ...props
      });

      // Update portfolio holdings only if the status is 'Settled'
      if (reward.status === "Settled") {
        await Portfolio.findByIdAndUpdate(portfolio.id, {
          $push: {
            holdings: [
              {
                assetCommonId: reward.asset,
                quantity: reward.quantity
              }
            ]
          }
        });
      }
    });
  }

  public withSavings(props?: Partial<SavingsTopupTransactionDTOInterface>): UserBuilder {
    return this._chain(async () => {
      const portfolio = await Portfolio.findOne({ owner: this._user.id });
      const currency = this._user.currency;
      const commonId =
        this._user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
          ? "mmf_dist_gbp"
          : "mmf_dist_eur";
      const savingsDefaults: SavingsTopupTransactionDTOInterface = {
        owner: this._user.id,
        portfolio: portfolio.id,
        status: "Settled",
        savingsProduct: commonId,
        consideration: {
          currency: currency,
          amount: faker.number.int({ min: 1, max: 10000 })
        },
        createdAt: new Date(),
        ...props
      };

      if (savingsDefaults.status === "Settled") {
        savingsDefaults.settledAt = new Date();
        await Portfolio.findByIdAndUpdate(portfolio.id, {
          $set: {
            savings: new Map([[commonId, { amount: savingsDefaults.consideration.amount, currency: "GBX" }]])
          }
        });
      }
      if (!SavingsProduct.findOne({ commonId })) {
        await buildSavingsProduct(true, { commonId: commonId });
      }

      await buildSavingsTopup(savingsDefaults, null, true);
    });
  }

  public withGift(giftProps?: Partial<GiftDTOInterface>): UserBuilder {
    return this._chain(async () => {
      await buildGift({
        targetUserEmail: this._user.email,
        consideration: {
          currency: "GBP",
          amount: 2000 // Default amount of 20 GBP (in pence)
        },
        message: faker.lorem.words(5),
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL]
        },
        ...giftProps
      });
    });
  }

  private withPortfolioTickers(): UserBuilder {
    return this._chain(async () => {
      const portfolio = await Portfolio.findOne({ owner: this._user.id });

      // Calculate number of tickers needed
      const DAYS = new Decimal(60);
      const MINUTES_INTERVAL = new Decimal(10);
      const MINUTES_IN_DAY = new Decimal(24).mul(60);
      const TICKERS_PER_DAY = MINUTES_IN_DAY.div(MINUTES_INTERVAL);
      const TOTAL_TICKERS = DAYS.mul(TICKERS_PER_DAY).toNumber();

      // Generate price points and reverse them so older dates have lower prices
      const intradayPricePoints = UserBuilder._generatePricePoints(TOTAL_TICKERS).reverse();

      // Create daily tickers using the last price of each day
      const dailyPrices = Array.from({ length: DAYS.toNumber() }).map((_, dayIndex) => {
        const startIndex = dayIndex * TICKERS_PER_DAY.toNumber();
        const endIndex = startIndex + TICKERS_PER_DAY.toNumber() - 1;
        // Use the last price of each day
        return intradayPricePoints[endIndex];
      });

      // Store daily ticker dates and prices for use in withDailySummaries
      Array.from({ length: DAYS.toNumber() }).forEach((_, index) => {
        const date = new Date();
        date.setDate(date.getDate() - (DAYS.toNumber() - index - 1)); // Start from 60 days ago
        // Set time to end of day for consistency
        date.setHours(23, 59, 59, 999);
        // Store price by date string
        this._data.portfolioTickerData[date.toISOString()] = dailyPrices[index];
      });

      // Create intraday tickers first
      await Promise.all([
        buildIntraDayPortfolioTickersBulk(portfolio, intradayPricePoints),
        buildDailyPortfolioTickersBulk(portfolio, dailyPrices)
      ]);
    });
  }

  // ***************
  // PRIVATE METHODS
  // ***************

  // ***** User Creation Methods *****

  private _createUserDocument(overrides: Partial<UserInterface> = {}): UserBuilder {
    if (this._user) {
      throw new Error("User document has already been created");
    }

    return this._chain(async () => {
      const auth0 = await this._retrieveAuth0User(overrides.email);
      const companyEntity = ConfigUtil.getDefaultCompanyEntity(overrides.residencyCountry);
      const currency = ConfigUtil.getDefaultUserCurrency(overrides.residencyCountry);
      this._user = await new User({ ...overrides, companyEntity, currency, auth0 }).save();
    });
  }

  private _createNewUser(email: string): UserBuilder {
    this._createUserDocument({ email }).withParticipant();
    return this;
  }

  private _createVerifiedUser(email: string, userProps?: Partial<UserInterface>): UserBuilder {
    if (this._user) {
      throw new Error("User document has already been created");
    }
    const residencyCountry = userProps?.residencyCountry ?? "GB";

    this._createUserDocument(
      buildUserDTO({
        email,
        dateOfBirth: new Date("1980-01-01"),
        hasAcceptedTerms: true,
        hasSeenBilling: false,
        isPassportVerified: true,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        kycStatus: KycStatusEnum.PASSED,
        nationalities: ["GR"],
        portfolioConversionStatus: "notStarted",
        residencyCountry,
        viewedWelcomePage: true,
        viewedKYCSuccessPage: true,
        viewedReferralCodeScreen: true,
        viewedWealthybitesScreen: true,
        activeProviders: [
          ProviderEnum.GOCARDLESS,
          ProviderEnum.SUMSUB,
          ProviderEnum.STRIPE,
          ProviderEnum.WEALTHKERNEL
        ],
        providers: {
          [ProviderEnum.GOCARDLESS]: {
            id: faker.string.uuid()
          },
          [ProviderEnum.SUMSUB]: {
            id: faker.string.uuid()
          },
          [ProviderEnum.STRIPE]: {
            id: faker.string.uuid()
          },
          [ProviderEnum.WEALTHKERNEL]: {
            id: faker.string.uuid() // TODO: fetch from WK
          }
        },
        ...userProps
      })
    )
      .withParticipant()
      .withKycOperation()
      .withAddress()
      .withAccount()
      .withPortfolio()
      .withWallet();
    return this;
  }

  private _createInvestedUser(email: string, userProps?: Partial<UserInterface>): UserBuilder {
    if (this._user) {
      throw new Error("User document has already been created");
    }

    // Create a verified user with bank account first
    this._createVerifiedUser(email, {
      ...userProps,
      portfolioConversionStatus: "completed"
    })
      .withSubscription()
      .withPortfolio({
        initialHoldingsAllocation: [
          { assetCommonId: "equities_apple", percentage: 40 },
          { assetCommonId: "equities_microsoft", percentage: 30 },
          { assetCommonId: "equities_us", percentage: 30 }
        ]
      })
      .withInvestment()
      .withPortfolioTickers()
      .withDailySummaries();

    return this;
  }

  // ***** Utils *****

  private _chain(taskFn: () => Promise<void>): UserBuilder {
    this._taskQueue.push(taskFn);
    return this;
  }

  /**
   * @description Deletes all user related db documents for the given email
   * @param email
   * @returns void
   */
  public async reset(email: string): Promise<void> {
    const user = await User.findOne({ email }).populate("portfolios");
    if (!user) {
      return;
    }

    // delete all documents with owner the given user
    await Promise.all([
      Account.deleteMany({ owner: user.id }),
      Address.deleteMany({ owner: user.id }),
      Automation.deleteMany({ owner: user.id }),
      BankAccount.deleteMany({ owner: user.id }),
      Gift.deleteMany({ targetUserEmail: email }),
      KycOperation.deleteMany({ owner: user.id }),
      Mandate.deleteMany({ owner: user.id }),
      Participant.deleteMany({ email }),
      PaymentMethod.deleteMany({ owner: user.id }),
      Portfolio.deleteMany({ owner: user.id }),
      ReferralCode.deleteMany({ owner: user.id }),
      RewardInvitation.deleteMany({ $or: [{ referrer: user.id }, { targetUserEmail: email }] }),
      Reward.deleteMany({ $or: [{ referrer: user.id }, { referral: user.id }] }),
      RiskAssessment.deleteMany({ owner: user.id }),
      Subscription.deleteMany({ owner: user.id }),
      UserDataRequest.deleteMany({ owner: user.id })
    ]);

    const portfolio = user.portfolios[0];
    if (portfolio) {
      await DailyPortfolioTicker.deleteMany({ portfolio: portfolio.id });

      const transactions = await Transaction.find({ owner: user.id });
      await Order.deleteMany({ transaction: { $in: transactions.map((transaction) => transaction.id) } });
      Transaction.deleteMany({ owner: user.id });
    }

    await User.deleteOne({ email });
  }

  // ***** External Service Methods *****

  private async _retrieveAuth0User(email: string): Promise<Auth0ObjectType> {
    const auth0Data = (await auth0ManagementClient.usersByEmail.getByEmail({ email })).data;
    if (!auth0Data[0]) {
      throw new Error("You need to first have created a user to auth0 to use this email.");
    }

    const auth0UserId = auth0Data[0].user_id as string;
    const [provider] = auth0UserId.split("|");

    return {
      id: auth0UserId,
      [provider]: auth0UserId
    };
  }

  /**
   * Generates an array of realistic price points with a slight upward bias and volatility
   * @param count Number of price points to generate
   * @param startPrice Initial price to start from
   * @returns Array of price points
   */
  private static _generatePricePoints(count: number, startPrice = 150): number[] {
    const MAX_MULTIPLE = 10;
    let price: Decimal = new Decimal(startPrice);
    let lowestPrice: Decimal = price;
    let highestPrice: Decimal = price;

    return Array.from({ length: count }).map((_, index) => {
      if (index === 0) {
        return price.toNumber();
      }

      // Calculate allowed ranges based on 10x multiple constraint
      const maxAllowedPrice = lowestPrice.mul(MAX_MULTIPLE);
      const minAllowedPrice = highestPrice.div(MAX_MULTIPLE);

      // Adjust bias based on where we are in the allowed range
      // If we're closer to the max allowed, bias downward
      const rangePosition = price.minus(minAllowedPrice).div(maxAllowedPrice.minus(minAllowedPrice));
      const upwardBias = rangePosition.gt(0.7) ? 0.3 : rangePosition.lt(0.3) ? 0.7 : 0.5;

      const isIncrease = faker.number.float() < upwardBias;

      // Smaller volatility to prevent huge swings
      const baseVolatility = 0.008;
      const volatility = faker.number.float() < 0.1 ? baseVolatility * 2 : baseVolatility;
      const maxChange: Decimal = price.mul(volatility);

      // Generate change
      const change: Decimal = isIncrease
        ? new Decimal(faker.number.float({ min: 0, max: 1 })).mul(maxChange)
        : new Decimal(faker.number.float({ min: -1, max: 0 })).mul(maxChange);

      // Constrain the new price within our allowed range
      let newPrice = price.plus(change);
      if (newPrice.gt(maxAllowedPrice)) {
        newPrice = maxAllowedPrice;
      } else if (newPrice.lt(minAllowedPrice)) {
        newPrice = minAllowedPrice;
      }

      // Update price and tracking variables
      price = newPrice;
      if (price.lt(lowestPrice)) lowestPrice = price;
      if (price.gt(highestPrice)) highestPrice = price;

      return price.toNumber();
    });
  }
}
