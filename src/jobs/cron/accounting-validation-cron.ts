import <PERSON>ronJob from "./cronJob";
import { CronJobNameEnum } from "../configs/cronNames";
import logger from "../../external-services/loggerService";
import { AccountingValidationCronService } from "../services/accountingValidationCronService";

class AccountingValidationCronJob extends CronJob {
  cronName = CronJobNameEnum.ACCOUNTING_VALIDATION;

  /**
   * @description Cron job for validating accounting DB entries against ledger entries
   * Runs every 30 minutes from 8 AM to 8 PM UTC during market hours
   * Reports discrepancies to Sentry and logs them to DataDog
   */
  async processFn(): Promise<void> {
    logger.info("🔍 Starting accounting validation...", {
      module: `cron:${this.cronName}`
    });

    await AccountingValidationCronService.runValidationOnDbWithLedger();

    logger.info("✅ Accounting validation completed successfully", {
      module: `cron:${this.cronName}`
    });
  }
}

export default AccountingValidationCronJob;
